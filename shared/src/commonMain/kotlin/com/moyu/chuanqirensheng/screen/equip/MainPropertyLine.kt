package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import com.moyu.chuanqirensheng.screen.property.PropertyItem
import com.moyu.core.AppWrapper
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import com.moyu.core.model.property.MAIN_ATTRIBUTE_STRENGTH
import com.moyu.core.model.property.MAIN_ATTRIBUTE_AGILITY
import com.moyu.core.model.property.MAIN_ATTRIBUTE_INTELLIGENCE
import core.generated.resources.attack1
import core.generated.resources.attack1_tips
import core.generated.resources.attack2
import core.generated.resources.attack2_tips
import core.generated.resources.attack3
import core.generated.resources.attack_tips
import core.generated.resources.defense1_tips
import core.generated.resources.dodge
import core.generated.resources.dodge_tips
import core.generated.resources.fatal_damage
import core.generated.resources.fatal_damage_tips
import core.generated.resources.fatal_rate
import core.generated.resources.fatal_rate_tips
import core.generated.resources.hp
import core.generated.resources.hp_tips
import core.generated.resources.speed
import core.generated.resources.speed_tips
import shared.generated.resources.Res
import shared.generated.resources.battle_attribute_1
import shared.generated.resources.battle_attribute_2
import shared.generated.resources.battle_attribute_3
import shared.generated.resources.battle_attribute_4
import shared.generated.resources.battle_attribute_5
import shared.generated.resources.battle_attribute_6
import shared.generated.resources.battle_attribute_7
import shared.generated.resources.battle_attribute_8
import shared.generated.resources.battle_attribute_9
import kotlin.math.max
import core.generated.resources.Res as CoreRes

@Composable
fun Property.MainPropertyLine(
    originProperty: Property = Property(),
    showBoost: Boolean = false,
    countStart: Int = 0,
    countEnd: Int = 100,
    textStyle: TextStyle = MaterialTheme.typography.h3,
    showZero: Boolean = true,
    showNegative: Boolean = false,
    textColor: Color = Color.Black,
    showIcon: Boolean = true,
) {
    val minValue = if (showNegative) -999999.0 else 0.0
    var count = 1
    if (count in countStart until countEnd) {
        if (showZero || attack1.toDouble() - originProperty.attack1.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_1,
            getProperty = { max(minValue, attack1.toDouble() - originProperty.attack1.toDouble()) },
            name = AppWrapper.getStringKmp(CoreRes.string.attack1),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.attack1_tips) },
            isBoost = { (attack1.toDouble() - originProperty.attack1) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || attack2.toDouble() - originProperty.attack2.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_2,
            getProperty = { max(minValue, attack2.toDouble() - originProperty.attack2.toDouble()) },
            name = AppWrapper.getStringKmp(CoreRes.string.attack2),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.attack2_tips) },
            isBoost = { (attack2.toDouble() - originProperty.attack2) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || attack3.toDouble() - originProperty.attack3.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_3,
            getProperty = { max(minValue, attack3.toDouble() - originProperty.attack3.toDouble()) },
            name = AppWrapper.getStringKmp(CoreRes.string.attack3),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.attack_tips) },
            isBoost = { (attack3.toDouble() - originProperty.attack3) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses.toDouble() - originProperty.defenses.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_4,
            getProperty = {
                max(
                    minValue,
                    defenses.toDouble() - originProperty.defenses.toDouble()
                )
            },
            name = DamageType.DamageType1.defenseName,
            getTips = { AppWrapper.getStringKmp(CoreRes.string.defense1_tips) },
            isBoost = { (defenses.toDouble() - originProperty.defenses) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || hp.toDouble() - originProperty.hp.toDouble() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_5,
            getProperty = { hp.toDouble() - originProperty.hp },
            name = AppWrapper.getStringKmp(CoreRes.string.hp),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.hp_tips) },
            isBoost = { (hp.toDouble() - originProperty.hp) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalRate() - originProperty.getRealFatalRate() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_6,
            getProperty = { max(minValue, getRealFatalRate() - originProperty.getRealFatalRate()) },
            name = AppWrapper.getStringKmp(CoreRes.string.fatal_rate),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.fatal_rate_tips) },
            showPercent = true,
            isBoost = { (getRealFatalRate() - originProperty.getRealFatalRate()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalDamage() - originProperty.getRealFatalDamage() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_7,
            getProperty = { max(minValue, getRealFatalDamage() - originProperty.getRealFatalDamage()) },
            name = AppWrapper.getStringKmp(CoreRes.string.fatal_damage),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.fatal_damage_tips) },
            showPercent = true,
            isBoost = { (getRealFatalDamage() - originProperty.getRealFatalDamage()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealDodgeRate() - originProperty.getRealDodgeRate() != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_8,
            getProperty = { max(minValue, getRealDodgeRate() - originProperty.getRealDodgeRate()) },
            name = AppWrapper.getStringKmp(CoreRes.string.dodge),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.dodge_tips) },
            showPercent = true,
            isBoost = { (getRealDodgeRate() - originProperty.getRealDodgeRate()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || speed.toDouble() - originProperty.speed != 0.0) PropertyItem(
            icon = Res.drawable.battle_attribute_9,
            getProperty = { max(minValue, speed.toDouble() - originProperty.speed) },
            name = AppWrapper.getStringKmp(CoreRes.string.speed),
            getTips = { AppWrapper.getStringKmp(CoreRes.string.speed_tips) },
            isBoost = { (speed.toDouble() - originProperty.speed) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        // 主属性显示 - 根据主属性类型显示对应的属性值
        if (showZero || getMainAttributeValue().toDouble() - originProperty.getMainAttributeValue() != 0.0) {
            val mainAttrIcon = when (mainAttributeType) {
                MAIN_ATTRIBUTE_STRENGTH -> Res.drawable.battle_attribute_1
                MAIN_ATTRIBUTE_AGILITY -> Res.drawable.battle_attribute_2
                MAIN_ATTRIBUTE_INTELLIGENCE -> Res.drawable.battle_attribute_3
                else -> Res.drawable.battle_attribute_1
            }

            PropertyItem(
                icon = mainAttrIcon,
                getProperty = { max(minValue, getMainAttributeValue().toDouble() - originProperty.getMainAttributeValue()) },
                name = "主属性(${getMainAttributeTypeName()})",
                getTips = { "当前英雄的主属性类型为${getMainAttributeTypeName()}，影响主要战斗能力" },
                isBoost = { (getMainAttributeValue().toDouble() - originProperty.getMainAttributeValue()) },
                showBoost = showBoost,
                textStyle = textStyle, textColor = textColor, showIcon = showIcon
            )
        }
    }
}