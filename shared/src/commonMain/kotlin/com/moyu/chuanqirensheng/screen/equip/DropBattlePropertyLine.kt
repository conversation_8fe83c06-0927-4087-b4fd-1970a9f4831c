package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.screen.award.AwardUIParam
import com.moyu.chuanqirensheng.screen.award.SingleAwardItem
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.core.AppWrapper
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import com.moyu.core.util.percentValueToDotWithNoDigits
import core.generated.resources.attack
import core.generated.resources.dodge
import core.generated.resources.fatal_damage
import core.generated.resources.fatal_rate
import core.generated.resources.hp
import core.generated.resources.speed
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.battle_attribute_1
import shared.generated.resources.battle_attribute_2
import shared.generated.resources.battle_attribute_3
import shared.generated.resources.battle_attribute_4
import shared.generated.resources.battle_attribute_5
import shared.generated.resources.battle_attribute_6
import shared.generated.resources.battle_attribute_7
import shared.generated.resources.battle_attribute_8
import shared.generated.resources.battle_attribute_9
import shared.generated.resources.master
import core.generated.resources.Res as CoreRes

@Composable
fun Property.DropBattlePropertyLine(param: AwardUIParam = defaultParam) {
    this.attack1.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_1,
            name = stringResource(Res.string.master) + stringResource(CoreRes.string.attack),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(),
        )
    }
    this.attack2.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_2,
            name = stringResource(Res.string.master) + stringResource(CoreRes.string.attack),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(),
        )
    }
    this.attack3.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_3,
            name = stringResource(Res.string.master) + stringResource(CoreRes.string.attack),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(),
        )
    }
    this.defenses.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_4,
            name = stringResource(Res.string.master) + DamageType.DamageType1.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.hp.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_5,
            name = stringResource(Res.string.master) + AppWrapper.kmpStringResource(CoreRes.string.hp),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.getRealFatalRate().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_6,
            name = stringResource(Res.string.master) + AppWrapper.kmpStringResource(CoreRes.string.fatal_rate),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithNoDigits(),
            param = param,
        )
    }
    this.getRealFatalDamage().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_7,
            name = stringResource(Res.string.master) + AppWrapper.kmpStringResource(CoreRes.string.fatal_damage),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithNoDigits(),
            param = param,
        )
    }
    this.getRealDodgeRate().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_8,
            name = stringResource(Res.string.master) + AppWrapper.kmpStringResource(CoreRes.string.dodge),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithNoDigits(),
            param = param,
        )
    }
    this.speed.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = Res.drawable.battle_attribute_9,
            name = stringResource(Res.string.master) + AppWrapper.kmpStringResource(CoreRes.string.speed),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
}