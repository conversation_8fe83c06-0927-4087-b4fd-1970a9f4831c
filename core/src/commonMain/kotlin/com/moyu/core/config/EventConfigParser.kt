package com.moyu.core.config

import com.moyu.core.model.Event

class EventConfigParser : ConfigParser<Event> {
    override fun parse(line: String): Event {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val name = words[i++]
        val age = words[i++].split(",").map { it.toInt() }
        val appear = words[i++].split(",").map { it.toInt() }
        val same = words[i++].toInt()
        val front = words[i++].split(",").map { it.toInt() }
        val disappear = words[i++].split(",").map { it.toInt() }
        val weight = words[i++].toInt()
        val condition = words[i++].toInt()
        val play = words[i++].toInt()
        val playPara1 = words[i++].split(",").map { it.toInt() }
        val playPara2 = words[i++].split(",").map { it.toDouble() }
        val winReward = words[i++].split(",").map { it.toInt() }
        val loseReward = words[i++].split(",").map { it.toInt() }
        val dialogId = words[i++].toInt()
        val dialogType = words[i++].toInt()
        val showText = words[i++]
        val startText = words[i++]
        val winText = words[i++]
        val loseText = words[i++]
        val isEnd = words[i++].toInt() == 1
        val endType = words[i++].toInt()
        val storyDesc1 = words[i++]
        val storyDesc2 = words[i++]
        val storyBag = words[i++].toInt()
        val eventAttribute1 = words[i++].toInt()
        val eventAttribute2 = words[i++].toInt()
        val eventAttribute3 = words[i++].toInt()
        val eventAttribute4 = words[i++].toInt()
        val eventAttribute5 = words[i++].toInt()
        val eventAttribute6 = words[i++].toDouble()
        val eventAttribute7 = words[i++].toDouble()
        val eventAttribute8 = words[i++].toDouble()
        val eventAttribute9 = words[i++].toInt()
        val pic = words[i++]
        val bgPic = words[i++]
        val isMainLine = words[i++].toInt()
        val isRepeat = words[i++].toInt()
        val front2 = words[i].split(",").map { it.toInt() }
        return Event(
            id,
            name,
            age,
            appear,
            same,
            front,
            disappear,
            weight,
            condition,
            play,
            playPara1,
            playPara2,
            winReward,
            loseReward,
            dialogId,
            dialogType,
            showText,
            startText,
            winText,
            loseText,
            isEnd,
            endType,
            storyDesc1,
            storyDesc2,
            storyBag,
            eventAttribute1,
            eventAttribute2,
            eventAttribute3,
            eventAttribute4,
            eventAttribute5,
            eventAttribute6,
            eventAttribute7,
            eventAttribute8,
            eventAttribute9,
            pic,
            bgPic,
            isMainLine,
            isRepeat,
            front2
        )
    }
}