package com.moyu.core.config

import com.moyu.core.model.Equipment

class EquipmentConfigParser : ConfigParser<Equipment> {
    override fun parse(line: String): Equipment {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val name = words[i++].trim()
        val requestId = words[i++].trim().toInt()
        val requestNum = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val attribute1 = words[i++].trim().toInt()
        val attribute2 = words[i++].trim().toInt()
        val attribute3 = words[i++].trim().toInt()
        val attribute4 = words[i++].trim().toInt()
        val attribute5 = words[i++].trim().toInt()
        val attribute6 = words[i++].trim().toDouble()
        val attribute7 = words[i++].trim().toDouble()
        val attribute8 = words[i++].trim().toDouble()
        val attribute9 = words[i++].trim().toInt()
        val star = words[i++].trim().toInt()
        val starLimit = words[i++].trim().toInt()
        val quality = words[i++].trim().toInt()
        val skillEffect = words[i++].trim().toInt()
        val starUpNum = words[i++].trim().toInt()
        val starUpResourceNum = words[i++].trim().toInt()
        val dropLimit = words[i++].trim().toInt()
        val story = words[i++].trim()
        val belong = words[i++].trim().toInt()
        val pic = words[i].trim()
        return Equipment(
            id,
            mainId,
            name,
            requestId,
            requestNum,
            type,
            attribute1,
            attribute2,
            attribute3,
            attribute4,
            attribute5,
            attribute6,
            attribute7,
            attribute8,
            attribute9,
            star,
            starLimit,
            quality,
            skillEffect,
            starUpNum,
            starUpResourceNum,
            dropLimit,
            story,
            belong,
            pic
        )
    }
}