package com.moyu.core.model.property

import kotlinx.serialization.Serializable

/**
 * 主属性增减值
 * 用于表示主属性的变化量，在应用时会根据目标Property的主属性类型来增减对应的属性
 */
@Serializable
data class MainPropertyDiff(
    val diffValue: Int
) {
    companion object {
        fun create(diffValue: Double): MainPropertyDiff {
            return MainPropertyDiff(diffValue.toInt())
        }
    }
}

/**
 * 主属性值
 * 用于表示主属性的具体数值，在应用时会根据目标Property的主属性类型来设置对应的属性
 */
@Serializable
data class MainPropertyValue(
    val value: Int
) {
    companion object {
        fun create(value: Double): MainPropertyValue {
            return MainPropertyValue(value.toInt())
        }
    }
}
