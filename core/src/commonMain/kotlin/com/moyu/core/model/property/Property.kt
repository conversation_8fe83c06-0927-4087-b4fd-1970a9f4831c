package com.moyu.core.model.property

import com.moyu.core.AppWrapper
import com.moyu.core.logic.buff.DAMAGE_PIERCE_BASE
import com.moyu.core.logic.buff.DOUBLE_SKILL
import com.moyu.core.model.damage.DamageType
import com.moyu.core.util.perMinusD
import com.moyu.core.util.perMinusI
import com.moyu.core.util.perPlusD
import com.moyu.core.util.perPlusI
import com.moyu.core.util.realValueToDotWithOneDigits
import core.generated.resources.Res
import core.generated.resources.attack1
import core.generated.resources.attack2
import core.generated.resources.attack3
import core.generated.resources.defense1
import core.generated.resources.dodge
import core.generated.resources.fatal_damage
import core.generated.resources.fatal_rate
import core.generated.resources.hp
import core.generated.resources.speed
import kotlinx.serialization.Serializable
import kotlin.math.min
import kotlin.math.roundToInt

const val MAX_DODGE = 80.0
const val MAX_CRITIC = 999999.0
const val MAX_DECREASE = 80.0

// 主属性类型枚举
const val MAIN_ATTRIBUTE_STRENGTH = 1  // 力量
const val MAIN_ATTRIBUTE_AGILITY = 2   // 敏捷
const val MAIN_ATTRIBUTE_INTELLIGENCE = 3  // 智力

val emptyDoubleList = listOf(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
val emptyControlList = listOf(0, 0, 0, 0, 0, 0)

val EMPTY_PROPERTY = Property()

@Serializable
data class Property(
    val speed: Int = 0,
    val hp: Int = 0,
    val attack1: Int = 0, // 力量
    val attack2: Int = 0, // 敏捷
    val attack3: Int = 0, // 智力
    val fatalRate: Double = 0.0,
    val fatalDamage: Double = 0.0,
    val dodgeRate: Double = 0.0,
    val suckBloodRate: Double = 0.0,
    val activeSkillTimes: Int = 0,
    val activeSkillRate: Double = 0.0,
    val triggerSkillRate: Double = 0.0,
    val normalAttackTimes: Int = 0,
    val normalAttackDamage: Double = 0.0,
    val defenses: Int = 0,
    val pierces: List<Double> = emptyDoubleList,
    val damageIncs: List<Double> = emptyDoubleList,
    val allDamage: Double = 0.0,
    val reduceDamageIncs: List<Double> = emptyDoubleList,
    val allReduceDamage: Double = 0.0,
    val raceDamageIncs: List<Double> = emptyDoubleList,
    val raceReduceDamageInc: List<Double> = emptyDoubleList,
    val controlEffects: List<Int> = emptyControlList,
    val buffEffect: Int = 0,
    val debuffEffect: Int = 0,
    val coolDownEffect: Int = 0,
    val healEffect: Double = 0.0,
    val shieldEffect: Double = 0.0, // 立场增减在这里仅限展示用，不参与实际计算
    val mainAttributeType: Int = MAIN_ATTRIBUTE_STRENGTH, // 主属性类型：1=力量，2=敏捷，3=智力
) {
    companion object {
        fun getDiffPropertyByBuffId(buffId: Int, diffDouble: Double): Property {
            val diff = diffDouble.roundToInt()
            return when (buffId) {
                // 属性增减
                in 2001..2015 -> propByIndex(buffId - 2000, diffDouble)
                in 2101..2115 -> propByIndex(buffId - 2100, -diffDouble)
                // 类型伤害增减
                3000 -> Property(allDamage = diffDouble)
                in 3001..3010 -> typeDamageByIndex(buffId - 3001, diffDouble)
                3100 -> Property(allDamage = -diffDouble)
                in 3101..3110 -> typeDamageByIndex(buffId - 3101, -diffDouble)
                3200 -> Property(allReduceDamage = diffDouble)
                in 3201..3210 -> typeReduceDamageByIndex(buffId - 3201, diffDouble)
                3300 -> Property(allReduceDamage = -diffDouble)
                in 3301..3310 -> typeReduceDamageByIndex(buffId - 3301, -diffDouble)
                //种族伤害增减
                in 3401..3410 -> raceDamageByIndex(buffId - 3401, diffDouble)
                in 3501..3510 -> raceDamageByIndex(buffId - 3501, -diffDouble)
                in 3601..3610 -> raceReduceDamageByIndex(buffId - 3601, diffDouble)
                in 3701..3710 -> raceReduceDamageByIndex(buffId - 3701, -diffDouble)
                // 控制
                in 4301..4305 -> controlEffectByIndex(buffId - 4301, diff)
                in 4311..4315 -> controlEffectByIndex(buffId - 4311, -diff)
                // 穿透
                in DAMAGE_PIERCE_BASE..DAMAGE_PIERCE_BASE + 10 -> damagePierce(
                    buffId - DAMAGE_PIERCE_BASE,
                    diffDouble
                )
                // 治疗效果
                5401 -> Property(healEffect = diffDouble)
                5402 -> Property(healEffect = -diffDouble)
                // 力场效果
                5403 -> Property(shieldEffect = diffDouble)
                5404 -> Property(shieldEffect = -diffDouble)
                // 主动技能概率
                5405 -> Property(activeSkillRate = diffDouble)
                5406 -> Property(activeSkillRate = -diffDouble)
                // 触发技能概率
                5407 -> Property(triggerSkillRate = diffDouble)
                5408 -> Property(triggerSkillRate = -diffDouble)
                // buff/debuff
                5409 -> Property(buffEffect = diff)
                5410 -> Property(buffEffect = -diff)
                5411 -> Property(debuffEffect = diff)
                5412 -> Property(debuffEffect = -diff)
                // cd
                5413 -> Property(coolDownEffect = diff)
                5414 -> Property(coolDownEffect = -diff)
                DOUBLE_SKILL -> Property(activeSkillTimes = diff)
                5601 -> Property(normalAttackTimes = diff)
                5602 -> Property(normalAttackTimes = -diff)
                5603 -> Property(normalAttackDamage = diffDouble)
                5604 -> Property(normalAttackDamage = -diffDouble)
                else -> EMPTY_PROPERTY
            }
        }

        fun damagePierce(toInt: Int, diff: Double): Property {
            return Property(pierces = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        private fun controlEffectByIndex(toInt: Int, diff: Int): Property {
            return Property(controlEffects = emptyControlList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun typeDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(damageIncs = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun typeReduceDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(reduceDamageIncs = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun raceDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(raceDamageIncs = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun raceReduceDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(raceReduceDamageInc = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun propByIndex(toInt: Int, diff: Double): Property {
            return when (toInt) {
                1 -> {
                    // 力量
                    Property(attack1 = diff.roundToInt())
                }

                2 -> {
                    // 敏捷
                    Property(attack2 = diff.roundToInt())
                }

                3 -> {
                    // 智力
                    Property(attack3 = diff.roundToInt())
                }

                4 -> {
                    // 防御
                    Property(defenses = diff.roundToInt())
                }

                5 -> {
                    // 生命上限
                    Property(hp = diff.roundToInt())
                }

                6 -> {
                    // 暴击率
                    Property(fatalRate = diff)
                }

                7 -> {
                    // 暴击伤害
                    Property(fatalDamage = diff)
                }

                8 -> {
                    // 格挡率
                    Property(dodgeRate = diff)
                }

                9 -> {
                    // 速度
                    Property(speed = diff.roundToInt())
                }

                10 -> {
                    // 主属性 - 根据主属性类型返回对应的属性
                    Property(mainAttributeType = diff.roundToInt())
                }

                else -> {
                    Property()
                }
            }
        }

        fun getPropertyByEnum(type: Int, value: Double): Property {
            return when (type) {
                1 -> Property(attack1 = value.roundToInt())
                2 -> Property(attack2 = value.roundToInt())
                3 -> Property(attack3 = value.roundToInt())
                4 -> Property(defenses = value.roundToInt())
                5 -> Property(hp = value.roundToInt())
                6 -> Property(fatalRate = value)
                7 -> Property(fatalDamage = value)
                8 -> Property(dodgeRate = value)
                9 -> Property(speed = value.roundToInt())
                10 -> Property(mainAttributeType = value.roundToInt())
                else -> Property()
            }
        }
    }

    operator fun plus(diffProperty: Property): Property {
        return copy(
            speed = speed + diffProperty.speed,
            hp = hp + diffProperty.hp,
            attack1 = attack1 + diffProperty.attack1,
            attack2 = attack2 + diffProperty.attack2,
            attack3 = attack3 + diffProperty.attack3,
            fatalRate = fatalRate + diffProperty.fatalRate,
            fatalDamage = fatalDamage + diffProperty.fatalDamage,
            dodgeRate = dodgeRate + diffProperty.dodgeRate,
            suckBloodRate = suckBloodRate + diffProperty.suckBloodRate,
            activeSkillTimes = activeSkillTimes + diffProperty.activeSkillTimes,
            activeSkillRate = activeSkillRate + diffProperty.activeSkillRate,
            triggerSkillRate = triggerSkillRate + diffProperty.triggerSkillRate,

            defenses = defenses + diffProperty.defenses,
            pierces = pierces.perPlusD(diffProperty.pierces),
            damageIncs = damageIncs.perPlusD(diffProperty.damageIncs),
            allDamage = allDamage + diffProperty.allDamage,
            reduceDamageIncs = reduceDamageIncs.perPlusD(diffProperty.reduceDamageIncs),
            allReduceDamage = allReduceDamage + diffProperty.allReduceDamage,

            raceDamageIncs = raceDamageIncs.perPlusD(diffProperty.raceDamageIncs),
            raceReduceDamageInc = raceReduceDamageInc.perPlusD(diffProperty.raceReduceDamageInc),

            controlEffects = controlEffects.perPlusI(diffProperty.controlEffects),

            healEffect = healEffect + diffProperty.healEffect,
            shieldEffect = shieldEffect + diffProperty.shieldEffect,
            buffEffect = buffEffect + diffProperty.buffEffect,
            debuffEffect = debuffEffect + diffProperty.debuffEffect,
            coolDownEffect = coolDownEffect + diffProperty.coolDownEffect,
            normalAttackDamage = normalAttackDamage + diffProperty.normalAttackDamage,
            normalAttackTimes = normalAttackTimes + diffProperty.normalAttackTimes,
            mainAttributeType = if (diffProperty.mainAttributeType != MAIN_ATTRIBUTE_STRENGTH) diffProperty.mainAttributeType else mainAttributeType,
        )
    }

    operator fun minus(diffProperty: Property): Property {
        return copy(
            speed = speed - diffProperty.speed,
            hp = hp - diffProperty.hp,
            attack1 = attack1 - diffProperty.attack1,
            attack2 = attack2 - diffProperty.attack2,
            attack3 = attack3 - diffProperty.attack3,
            fatalRate = fatalRate - diffProperty.fatalRate,
            fatalDamage = fatalDamage - diffProperty.fatalDamage,
            dodgeRate = dodgeRate - diffProperty.dodgeRate,
            suckBloodRate = suckBloodRate - diffProperty.suckBloodRate,
            activeSkillTimes = activeSkillTimes - diffProperty.activeSkillTimes,
            activeSkillRate = activeSkillRate - diffProperty.activeSkillRate,
            triggerSkillRate = triggerSkillRate - diffProperty.triggerSkillRate,

            defenses = defenses - diffProperty.defenses,
            pierces = pierces.perMinusD(diffProperty.pierces),
            damageIncs = damageIncs.perMinusD(diffProperty.damageIncs),
            allDamage = allDamage - diffProperty.allDamage,
            reduceDamageIncs = reduceDamageIncs.perMinusD(diffProperty.reduceDamageIncs),
            allReduceDamage = allReduceDamage - diffProperty.allReduceDamage,

            raceDamageIncs = raceDamageIncs.perMinusD(diffProperty.raceDamageIncs),
            raceReduceDamageInc = raceReduceDamageInc.perMinusD(diffProperty.raceReduceDamageInc),

            controlEffects = controlEffects.perMinusI(diffProperty.controlEffects),

            healEffect = healEffect - diffProperty.healEffect,
            shieldEffect = shieldEffect - diffProperty.shieldEffect,
            buffEffect = buffEffect - diffProperty.buffEffect,
            debuffEffect = debuffEffect - diffProperty.debuffEffect,
            coolDownEffect = coolDownEffect - diffProperty.coolDownEffect,
            normalAttackDamage = normalAttackDamage - diffProperty.normalAttackDamage,
            normalAttackTimes = normalAttackTimes - diffProperty.normalAttackTimes,
            mainAttributeType = mainAttributeType, // 主属性类型在减法中保持不变
        )
    }

    fun getRealFatalRate(): Double {
        return min(MAX_CRITIC, fatalRate) / 100
    }

    fun getLimitedFatalRate(): Double {
        return min(MAX_CRITIC, fatalRate)
    }

    fun getRealFatalDamage(): Double {
        return fatalDamage / 100
    }

    fun getRealDodgeRate(): Double {
        return min(MAX_DODGE, dodgeRate) / 100
    }

    fun getLimitedDodgeRate(): Double {
        return min(MAX_DODGE, dodgeRate)
    }

    fun getRealSuckBloodRate(): Double {
        return suckBloodRate / 100
    }

    fun getDefenseByType(damageType: DamageType): Int {
        return defenses
    }

    fun getPierceByType(damageType: DamageType): Double {
        return pierces[damageType.value - 1]
    }

    fun getDamageIncreaseByRaceType(raceType: Int): Double {
        return raceDamageIncs[raceType - 1] / 100
    }

    fun getDamageIncreaseByType(damageType: DamageType): Double {
        return damageIncs[damageType.value - 1] / 100
    }

    fun getNormalAttackRate(): Double {
        return normalAttackDamage / 100
    }

    fun getReduceDamageIncreaseByRaceType(raceType: Int): Double {
        return min(raceReduceDamageInc[raceType - 1], MAX_DECREASE) / 100
    }

    fun getReduceDamageIncreaseByType(damageType: DamageType): Double {
        return min(reduceDamageIncs[damageType.value - 1], MAX_DECREASE) / 100
    }

    operator fun times(times: Double): Property {
        return this.copy(
            speed = (speed * times).roundToInt(),
            hp = (hp * times).roundToInt(),
            attack1 = (attack1 * times).roundToInt(),
            attack2 = (attack2 * times).roundToInt(),
            attack3 = (attack3 * times).roundToInt(),
            defenses = (defenses * times).roundToInt(),
            suckBloodRate = suckBloodRate * times,
            dodgeRate = dodgeRate * times,
            fatalDamage = fatalDamage * times,
            fatalRate = fatalRate * times,
            mainAttributeType = mainAttributeType, // 主属性类型在乘法中保持不变
        )
    }

    fun isNotEmpty(): Boolean {
        return this != EMPTY_PROPERTY
    }

    fun isEmpty(): Boolean {
        return this == EMPTY_PROPERTY
    }

    fun getPropertyByTarget(propIndex: Int): Double {
        return when (propIndex) {
            1 -> {
                // 力量
                attack1.toDouble()
            }

            2 -> {
                // 敏捷
                attack2.toDouble()
            }

            3 -> {
                // 智力
                attack3.toDouble()
            }

            4 -> {
                // 防御
                defenses.toDouble()
            }

            5 -> {
                // 生命上限
                hp.toDouble()
            }

            6 -> {
                // 暴击率
                fatalRate
            }

            7 -> {
                // 暴击伤害
                fatalDamage
            }

            8 -> {
                // 闪避率
                dodgeRate
            }

            9 -> {
                // 速度
                speed.toDouble()
            }

            10 -> {
                // 主属性 - 根据主属性类型返回对应的属性值
                getMainAttributeValue().toDouble()
            }

            else -> {
                error("无效effectReference")
            }
        }
    }

    fun getAllDamageRate(): Double {
        return allDamage / 100
    }

    fun getAllReduceDamageRate(): Double {
        return allReduceDamage / 100
    }

    /**
     * 获取主属性值
     */
    fun getMainAttributeValue(): Int {
        return when (mainAttributeType) {
            MAIN_ATTRIBUTE_STRENGTH -> attack1
            MAIN_ATTRIBUTE_AGILITY -> attack2
            MAIN_ATTRIBUTE_INTELLIGENCE -> attack3
            else -> attack1 // 默认返回力量
        }
    }

    /**
     * 设置主属性值
     */
    fun setMainAttributeValue(value: Int): Property {
        return when (mainAttributeType) {
            MAIN_ATTRIBUTE_STRENGTH -> copy(attack1 = value)
            MAIN_ATTRIBUTE_AGILITY -> copy(attack2 = value)
            MAIN_ATTRIBUTE_INTELLIGENCE -> copy(attack3 = value)
            else -> copy(attack1 = value) // 默认设置力量
        }
    }

    /**
     * 修改主属性值
     */
    fun changeMainAttributeValue(diff: Int): Property {
        return when (mainAttributeType) {
            MAIN_ATTRIBUTE_STRENGTH -> copy(attack1 = attack1 + diff)
            MAIN_ATTRIBUTE_AGILITY -> copy(attack2 = attack2 + diff)
            MAIN_ATTRIBUTE_INTELLIGENCE -> copy(attack3 = attack3 + diff)
            else -> copy(attack1 = attack1 + diff) // 默认修改力量
        }
    }

    /**
     * 获取主属性类型名称
     */
    fun getMainAttributeTypeName(): String {
        return when (mainAttributeType) {
            MAIN_ATTRIBUTE_STRENGTH -> "力量"
            MAIN_ATTRIBUTE_AGILITY -> "敏捷"
            MAIN_ATTRIBUTE_INTELLIGENCE -> "智力"
            else -> "力量"
        }
    }

    fun ensureNotNegative(): Property {
        return Property(
            speed = speed.coerceAtLeast(0),
            hp = hp.coerceAtLeast(0),
            attack1 = attack1.coerceAtLeast(0),
            attack2 = attack2.coerceAtLeast(0),
            attack3 = attack3.coerceAtLeast(0),
            fatalRate = fatalRate.coerceAtLeast(0.0).coerceAtMost(MAX_CRITIC),
            fatalDamage = fatalDamage.coerceAtLeast(0.0),
            dodgeRate = dodgeRate.coerceAtLeast(0.0).coerceAtMost(MAX_DODGE),
            suckBloodRate = suckBloodRate.coerceAtLeast(0.0),
            defenses = defenses.coerceAtLeast(0),
            mainAttributeType = mainAttributeType.coerceIn(MAIN_ATTRIBUTE_STRENGTH, MAIN_ATTRIBUTE_INTELLIGENCE),
        )
    }

    fun getNonZeroString(): String {
        val result = StringBuilder()

        // 辅助函数：处理 Int 类型
        fun StringBuilder.addStatIfNonZero(label: String, value: Int) {
            if (value != 0) {
                if (isNotEmpty()) append(", ")
                // 正数加 "+"，负数会自动带 "-"
                val sign = if (value > 0) "+" else ""  // 对负数无需手动加 "-"
                append(label).append(sign).append(value)
            }
        }

        // 辅助函数：处理 Double 类型
        fun StringBuilder.addStatIfNonZero(label: String, value: Double, suffix: String = "%") {
            // 这里用 0.0 判断，也可视需求看是否要对非常小的正负值做特殊处理
            if (value != 0.0) {
                if (isNotEmpty()) append(", ")
                val sign = if (value > 0) "+" else ""  // 负数会自动使用 "-"，无须额外处理
                val formatted = value.realValueToDotWithOneDigits() // 假设你已有该方法
                append(label).append(sign).append(formatted).append(suffix)
            }
        }

        // 在此填入实际的 label 和数值
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.attack1), attack1)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.attack2), attack2)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.attack3), attack3)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.hp), hp)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.speed), speed)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.fatal_rate), fatalRate)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.fatal_damage), fatalDamage)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.dodge), dodgeRate)
        result.addStatIfNonZero(AppWrapper.getStringKmp(Res.string.defense1), defenses)

        return result.toString()
    }
}