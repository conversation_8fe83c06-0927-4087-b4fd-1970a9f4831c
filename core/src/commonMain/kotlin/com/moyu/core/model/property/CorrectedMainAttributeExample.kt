package com.moyu.core.model.property

/**
 * 修正后的主属性功能示例
 * 展示正确的枚举10使用方式
 */
object CorrectedMainAttributeExample {
    
    fun demonstrateCorrectUsage() {
        println("=== 修正后的主属性功能演示 ===")
        
        // 创建不同主属性类型的英雄
        val warrior = Property(
            attack1 = 100, // 力量
            attack2 = 60,  // 敏捷
            attack3 = 40,  // 智力
            hp = 1000,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        val archer = Property(
            attack1 = 50,  // 力量
            attack2 = 120, // 敏捷
            attack3 = 70,  // 智力
            hp = 800,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY
        )
        
        val mage = Property(
            attack1 = 40,  // 力量
            attack2 = 50,  // 敏捷
            attack3 = 130, // 智力
            hp = 700,
            mainAttributeType = MAIN_ATTRIBUTE_INTELLIGENCE
        )
        
        println("初始状态:")
        println("战士 - 主属性类型: ${warrior.getMainAttributeTypeName()}, 主属性值: ${warrior.getMainAttributeValue()}")
        println("弓手 - 主属性类型: ${archer.getMainAttributeTypeName()}, 主属性值: ${archer.getMainAttributeValue()}")
        println("法师 - 主属性类型: ${mage.getMainAttributeTypeName()}, 主属性值: ${mage.getMainAttributeValue()}")
        
        // 通过枚举10获取主属性值
        println("\n通过枚举10获取主属性值:")
        println("战士主属性值: ${warrior.getPropertyByTarget(10)}")
        println("弓手主属性值: ${archer.getPropertyByTarget(10)}")
        println("法师主属性值: ${mage.getPropertyByTarget(10)}")
        
        // 通过枚举10创建主属性增减buff
        val mainAttrBuff = Property.propByIndex(10, 50.0) // 主属性增加50
        println("\n创建主属性增减buff (增加50):")
        println("buff标记: ${mainAttrBuff.normalAttackDamage}")
        println("buff数值: ${mainAttrBuff.normalAttackTimes}")
        
        // 应用buff到不同类型的英雄
        val buffedWarrior = warrior + mainAttrBuff
        val buffedArcher = archer + mainAttrBuff
        val buffedMage = mage + mainAttrBuff
        
        println("\n应用buff后:")
        println("战士:")
        println("  buff前 - attack1: ${warrior.attack1}, 主属性值: ${warrior.getMainAttributeValue()}")
        println("  buff后 - attack1: ${buffedWarrior.attack1}, 主属性值: ${buffedWarrior.getMainAttributeValue()}")
        
        println("弓手:")
        println("  buff前 - attack2: ${archer.attack2}, 主属性值: ${archer.getMainAttributeValue()}")
        println("  buff后 - attack2: ${buffedArcher.attack2}, 主属性值: ${buffedArcher.getMainAttributeValue()}")
        
        println("法师:")
        println("  buff前 - attack3: ${mage.attack3}, 主属性值: ${mage.getMainAttributeValue()}")
        println("  buff后 - attack3: ${buffedMage.attack3}, 主属性值: ${buffedMage.getMainAttributeValue()}")
        
        // 验证其他属性没有被影响
        println("\n验证其他属性没有被影响:")
        println("战士 - buff前后attack2: ${warrior.attack2} -> ${buffedWarrior.attack2}")
        println("弓手 - buff前后attack1: ${archer.attack1} -> ${buffedArcher.attack1}")
        println("法师 - buff前后attack1: ${mage.attack1} -> ${buffedMage.attack1}")
        
        // 演示buff系统中的使用
        demonstrateBuffSystem()
    }
    
    private fun demonstrateBuffSystem() {
        println("\n=== Buff系统中的使用 ===")
        
        // 模拟buff ID 2010 对应主属性增减
        val buffId = 2010
        val buffValue = 30.0
        
        // 通过getDiffPropertyByBuffId创建主属性buff
        val mainAttrBuff = Property.getDiffPropertyByBuffId(buffId, buffValue)
        
        val hero = Property(
            attack1 = 80,
            attack2 = 100,
            attack3 = 60,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY // 敏捷型
        )
        
        println("英雄类型: ${hero.getMainAttributeTypeName()}")
        println("buff前主属性值: ${hero.getMainAttributeValue()}")
        println("buff前各属性 - 力量: ${hero.attack1}, 敏捷: ${hero.attack2}, 智力: ${hero.attack3}")
        
        val buffedHero = hero + mainAttrBuff
        
        println("buff后主属性值: ${buffedHero.getMainAttributeValue()}")
        println("buff后各属性 - 力量: ${buffedHero.attack1}, 敏捷: ${buffedHero.attack2}, 智力: ${buffedHero.attack3}")
        println("可以看到只有敏捷属性增加了30，其他属性保持不变")
    }
    
    fun demonstrateErrorCases() {
        println("\n=== 错误用法演示 ===")
        
        try {
            // 这会抛出错误，因为getPropertyByEnum不支持枚举10
            val errorProperty = Property.getPropertyByEnum(10, 100.0)
            println("这行不应该被执行")
        } catch (e: Exception) {
            println("正确捕获错误: ${e.message}")
        }
        
        println("正确的做法是使用实例方法 getPropertyByTarget(10) 来获取主属性值")
    }
    
    fun runAllDemonstrations() {
        demonstrateCorrectUsage()
        demonstrateErrorCases()
        
        println("\n=== 总结 ===")
        println("1. 使用 Property.propByIndex(10, diffValue) 创建主属性增减")
        println("2. 使用 property.getPropertyByTarget(10) 获取主属性值")
        println("3. 主属性增减会根据目标的主属性类型自动应用到对应的属性上")
        println("4. 不要使用 Property.getPropertyByEnum(10, value)，它会抛出错误")
    }
}
