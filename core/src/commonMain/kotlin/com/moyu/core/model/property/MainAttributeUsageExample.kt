package com.moyu.core.model.property

/**
 * 主属性功能使用示例
 * 展示如何在游戏中使用新的主属性功能
 */
object MainAttributeUsageExample {
    
    /**
     * 示例1：创建不同类型的英雄属性
     */
    fun createHeroProperties() {
        // 创建力量型英雄
        val warriorProperty = Property(
            attack1 = 150,  // 力量
            attack2 = 80,   // 敏捷
            attack3 = 60,   // 智力
            hp = 1200,
            defenses = 100,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        // 创建敏捷型英雄
        val archerProperty = Property(
            attack1 = 70,   // 力量
            attack2 = 160,  // 敏捷
            attack3 = 90,   // 智力
            hp = 900,
            defenses = 70,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY
        )
        
        // 创建智力型英雄
        val mageProperty = Property(
            attack1 = 50,   // 力量
            attack2 = 70,   // 敏捷
            attack3 = 180,  // 智力
            hp = 800,
            defenses = 50,
            mainAttributeType = MAIN_ATTRIBUTE_INTELLIGENCE
        )
        
        println("战士主属性值: ${warriorProperty.getMainAttributeValue()} (${warriorProperty.getMainAttributeTypeName()})")
        println("弓箭手主属性值: ${archerProperty.getMainAttributeValue()} (${archerProperty.getMainAttributeTypeName()})")
        println("法师主属性值: ${mageProperty.getMainAttributeValue()} (${mageProperty.getMainAttributeTypeName()})")
    }
    
    /**
     * 示例2：使用枚举10来获取和修改主属性
     */
    fun useMainAttributeEnum() {
        val heroProperty = Property(
            attack1 = 100,
            attack2 = 80,
            attack3 = 60,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        // 通过枚举10获取主属性值
        val mainAttrValue = heroProperty.getPropertyByTarget(10)
        println("通过枚举10获取的主属性值: $mainAttrValue")
        
        // 通过枚举10创建属性增减效果
        val strengthBuff = Property.propByIndex(10, MAIN_ATTRIBUTE_STRENGTH.toDouble())
        val agilityBuff = Property.propByIndex(10, MAIN_ATTRIBUTE_AGILITY.toDouble())
        
        println("力量型buff的主属性类型: ${strengthBuff.mainAttributeType}")
        println("敏捷型buff的主属性类型: ${agilityBuff.mainAttributeType}")
    }
    
    /**
     * 示例3：在buff系统中使用主属性
     */
    fun useMainAttributeInBuffSystem() {
        // 原始属性
        val originalProperty = Property(
            attack1 = 100,
            attack2 = 80,
            attack3 = 60,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        // 创建一个增加主属性的buff
        val mainAttributeBuff = Property.createMainAttributeDiff(
            MAIN_ATTRIBUTE_STRENGTH, 
            50
        )
        
        // 应用buff
        val buffedProperty = originalProperty + mainAttributeBuff
        
        println("原始主属性值: ${originalProperty.getMainAttributeValue()}")
        println("buff后主属性值: ${buffedProperty.getMainAttributeValue()}")
        println("原始力量: ${originalProperty.attack1}")
        println("buff后力量: ${buffedProperty.attack1}")
    }
    
    /**
     * 示例4：装备系统中的主属性支持
     */
    fun equipmentMainAttributeExample() {
        // 假设有一个装备，需要根据英雄的主属性类型来提供对应的属性加成
        val heroProperty = Property(
            attack1 = 100,
            attack2 = 80,
            attack3 = 60,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY  // 敏捷型英雄
        )
        
        // 装备提供的主属性加成
        val equipmentBonus = 30
        
        // 根据英雄的主属性类型，给对应的属性加成
        val enhancedProperty = heroProperty.addMainAttributeValue(equipmentBonus)
        
        println("装备前敏捷: ${heroProperty.attack2}")
        println("装备后敏捷: ${enhancedProperty.attack2}")
        println("装备前主属性值: ${heroProperty.getMainAttributeValue()}")
        println("装备后主属性值: ${enhancedProperty.getMainAttributeValue()}")
    }
    
    /**
     * 示例5：技能系统中的主属性应用
     */
    fun skillMainAttributeExample() {
        val heroProperty = Property(
            attack1 = 120,
            attack2 = 90,
            attack3 = 70,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        // 技能伤害基于主属性值计算
        val skillDamageMultiplier = 1.5
        val skillBaseDamage = heroProperty.getMainAttributeValue() * skillDamageMultiplier
        
        println("英雄类型: ${heroProperty.getMainAttributeTypeName()}")
        println("主属性值: ${heroProperty.getMainAttributeValue()}")
        println("技能基础伤害: $skillBaseDamage")
        
        // 检查英雄类型
        when {
            heroProperty.isStrengthType() -> println("这是一个力量型英雄，适合近战")
            heroProperty.isAgilityType() -> println("这是一个敏捷型英雄，适合远程")
            heroProperty.isIntelligenceType() -> println("这是一个智力型英雄，适合法术")
        }
    }
    
    /**
     * 示例6：属性比较和分析
     */
    fun attributeAnalysisExample() {
        val hero1 = Property(
            attack1 = 150, attack2 = 80, attack3 = 60,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        val hero2 = Property(
            attack1 = 70, attack2 = 160, attack3 = 90,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY
        )
        
        // 比较主属性值
        val comparison = hero1.compareMainAttribute(hero2)
        println("英雄1主属性值: ${hero1.getMainAttributeValue()}")
        println("英雄2主属性值: ${hero2.getMainAttributeValue()}")
        println("比较结果: ${if (comparison > 0) "英雄1更强" else if (comparison < 0) "英雄2更强" else "相等"}")
        
        // 分析主属性占比
        println("英雄1主属性占比: ${String.format("%.2f", hero1.getMainAttributeRatio() * 100)}%")
        println("英雄2主属性占比: ${String.format("%.2f", hero2.getMainAttributeRatio() * 100)}%")
    }
}
