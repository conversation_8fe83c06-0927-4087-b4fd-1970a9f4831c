package com.moyu.core.model

import com.moyu.core.model.property.Property

data class Difficult(
    override val id: Int = 0,
    val name: String = "",
    val attribute1: Int = 0,
    val attribute2: Int = 0,
    val attribute3: Int = 0,
    val attribute4: Int = 0,
    val attribute5: Int = 0,
    val attribute6: Double = 0.0,
    val attribute7: Double = 0.0,
    val attribute8: Double = 0.0,
    val attribute9: Int = 0,
    val badgeType: Int = 0,
    val adv1: Int = 0,
    val adv2: Int = 0,
    val adv3: Int = 0,
    val adv4: Int = 0,
    val adv5: Int = 0,
    val resource1: Int = 0,
    val resource2: Int = 0,
): ConfigData {
    fun toProperty(): Property {
        return Property(
            attack1 = attribute1,
            attack2 = attribute2,
            attack3 = attribute3,
            defenses = attribute4,
            hp = attribute5,
            fatalRate = attribute6,
            fatalDamage = attribute7,
            dodgeRate = attribute8,
            speed = attribute9
        )
    }
}