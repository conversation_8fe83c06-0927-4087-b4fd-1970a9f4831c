package com.moyu.core.model.property

/**
 * 主属性相关的扩展函数
 */

/**
 * 创建指定主属性类型的Property
 */
fun Property.Companion.createWithMainAttribute(
    mainAttributeType: Int,
    mainAttributeValue: Int = 0,
    otherAttributes: Property = EMPTY_PROPERTY
): Property {
    return when (mainAttributeType) {
        MAIN_ATTRIBUTE_STRENGTH -> otherAttributes.copy(
            attack1 = mainAttributeValue,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        MAIN_ATTRIBUTE_AGILITY -> otherAttributes.copy(
            attack2 = mainAttributeValue,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY
        )
        MAIN_ATTRIBUTE_INTELLIGENCE -> otherAttributes.copy(
            attack3 = mainAttributeValue,
            mainAttributeType = MAIN_ATTRIBUTE_INTELLIGENCE
        )
        else -> otherAttributes.copy(
            attack1 = mainAttributeValue,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
    }
}

/**
 * 判断是否为力量型
 */
fun Property.isStrengthType(): Boolean = mainAttributeType == MAIN_ATTRIBUTE_STRENGTH

/**
 * 判断是否为敏捷型
 */
fun Property.isAgilityType(): Boolean = mainAttributeType == MAIN_ATTRIBUTE_AGILITY

/**
 * 判断是否为智力型
 */
fun Property.isIntelligenceType(): Boolean = mainAttributeType == MAIN_ATTRIBUTE_INTELLIGENCE

/**
 * 获取主属性对应的攻击属性名称
 */
fun Property.getMainAttributeAttackName(): String {
    return when (mainAttributeType) {
        MAIN_ATTRIBUTE_STRENGTH -> "attack1"
        MAIN_ATTRIBUTE_AGILITY -> "attack2"
        MAIN_ATTRIBUTE_INTELLIGENCE -> "attack3"
        else -> "attack1"
    }
}

/**
 * 根据主属性类型增加对应的攻击属性
 */
fun Property.addMainAttributeValue(value: Int): Property {
    return when (mainAttributeType) {
        MAIN_ATTRIBUTE_STRENGTH -> copy(attack1 = attack1 + value)
        MAIN_ATTRIBUTE_AGILITY -> copy(attack2 = attack2 + value)
        MAIN_ATTRIBUTE_INTELLIGENCE -> copy(attack3 = attack3 + value)
        else -> copy(attack1 = attack1 + value)
    }
}

/**
 * 根据主属性类型设置对应的攻击属性
 */
fun Property.withMainAttributeValue(value: Int): Property {
    return when (mainAttributeType) {
        MAIN_ATTRIBUTE_STRENGTH -> copy(attack1 = value)
        MAIN_ATTRIBUTE_AGILITY -> copy(attack2 = value)
        MAIN_ATTRIBUTE_INTELLIGENCE -> copy(attack3 = value)
        else -> copy(attack1 = value)
    }
}

/**
 * 获取主属性类型的图标资源名称（如果有的话）
 */
fun Property.getMainAttributeIconName(): String {
    return when (mainAttributeType) {
        MAIN_ATTRIBUTE_STRENGTH -> "battle_attribute_1"
        MAIN_ATTRIBUTE_AGILITY -> "battle_attribute_2"
        MAIN_ATTRIBUTE_INTELLIGENCE -> "battle_attribute_3"
        else -> "battle_attribute_1"
    }
}

/**
 * 创建主属性增减的Property（用于buff系统）
 */
fun Property.Companion.createMainAttributeDiff(
    mainAttributeType: Int,
    diffValue: Int
): Property {
    return when (mainAttributeType) {
        MAIN_ATTRIBUTE_STRENGTH -> Property(attack1 = diffValue, mainAttributeType = mainAttributeType)
        MAIN_ATTRIBUTE_AGILITY -> Property(attack2 = diffValue, mainAttributeType = mainAttributeType)
        MAIN_ATTRIBUTE_INTELLIGENCE -> Property(attack3 = diffValue, mainAttributeType = mainAttributeType)
        else -> Property(attack1 = diffValue, mainAttributeType = MAIN_ATTRIBUTE_STRENGTH)
    }
}

/**
 * 比较两个Property的主属性值
 */
fun Property.compareMainAttribute(other: Property): Int {
    return this.getMainAttributeValue().compareTo(other.getMainAttributeValue())
}

/**
 * 获取主属性在总属性中的占比
 */
fun Property.getMainAttributeRatio(): Double {
    val total = attack1 + attack2 + attack3
    return if (total > 0) {
        getMainAttributeValue().toDouble() / total
    } else {
        0.0
    }
}
