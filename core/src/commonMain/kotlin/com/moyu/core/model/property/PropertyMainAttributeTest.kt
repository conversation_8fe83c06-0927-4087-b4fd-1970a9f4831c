package com.moyu.core.model.property

/**
 * 主属性功能测试示例
 */
object PropertyMainAttributeTest {
    
    fun testMainAttribute() {
        // 测试创建不同主属性类型的Property
        val strengthProperty = Property(
            attack1 = 100,
            attack2 = 50,
            attack3 = 30,
            mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
        )
        
        val agilityProperty = Property(
            attack1 = 50,
            attack2 = 100,
            attack3 = 30,
            mainAttributeType = MAIN_ATTRIBUTE_AGILITY
        )
        
        val intelligenceProperty = Property(
            attack1 = 30,
            attack2 = 50,
            attack3 = 100,
            mainAttributeType = MAIN_ATTRIBUTE_INTELLIGENCE
        )
        
        // 测试获取主属性值
        println("力量型主属性值: ${strengthProperty.getMainAttributeValue()}") // 应该输出100
        println("敏捷型主属性值: ${agilityProperty.getMainAttributeValue()}") // 应该输出100
        println("智力型主属性值: ${intelligenceProperty.getMainAttributeValue()}") // 应该输出100
        
        // 测试获取主属性类型名称
        println("力量型主属性名称: ${strengthProperty.getMainAttributeTypeName()}")
        println("敏捷型主属性名称: ${agilityProperty.getMainAttributeTypeName()}")
        println("智力型主属性名称: ${intelligenceProperty.getMainAttributeTypeName()}")
        
        // 测试通过枚举10获取主属性值
        println("力量型通过枚举10获取: ${strengthProperty.getPropertyByTarget(10)}")
        println("敏捷型通过枚举10获取: ${agilityProperty.getPropertyByTarget(10)}")
        println("智力型通过枚举10获取: ${intelligenceProperty.getPropertyByTarget(10)}")
        
        // 测试修改主属性值
        val modifiedStrengthProperty = strengthProperty.changeMainAttributeValue(20)
        println("修改后力量型主属性值: ${modifiedStrengthProperty.getMainAttributeValue()}") // 应该输出120
        println("修改后力量型attack1: ${modifiedStrengthProperty.attack1}") // 应该输出120
        
        // 测试通过propByIndex创建主属性增减
        val mainAttrDiff = Property.propByIndex(10, 50.0) // 主属性增加50
        println("创建的主属性增减标记: ${mainAttrDiff.normalAttackDamage}") // 应该是特殊标记
        println("主属性增减值: ${mainAttrDiff.normalAttackTimes}") // 应该是50

        // 测试应用主属性增减到力量型英雄
        val buffedStrengthProperty = strengthProperty + mainAttrDiff
        println("buff前力量型attack1: ${strengthProperty.attack1}") // 应该输出100
        println("buff后力量型attack1: ${buffedStrengthProperty.attack1}") // 应该输出150
        println("buff后力量型主属性值: ${buffedStrengthProperty.getMainAttributeValue()}") // 应该输出150

        // 测试应用主属性增减到敏捷型英雄
        val buffedAgilityProperty = agilityProperty + mainAttrDiff
        println("buff前敏捷型attack2: ${agilityProperty.attack2}") // 应该输出100
        println("buff后敏捷型attack2: ${buffedAgilityProperty.attack2}") // 应该输出150
        println("buff后敏捷型主属性值: ${buffedAgilityProperty.getMainAttributeValue()}") // 应该输出150
    }
}
