package com.moyu.core.model

import com.moyu.core.model.property.Property

data class Tower(
    val id: Int,
    val layer: Int,
    val type: List<Int>,
    val playPara1: List<Int>,
    val playPara2: Int,
    val eventAttribute1: Int,
    val eventAttribute2: Int,
    val eventAttribute3: Int,
    val eventAttribute4: Int,
    val eventAttribute5: Int,
    val eventAttribute6: Double,
    val eventAttribute7: Double,
    val eventAttribute8: Double,
    val eventAttribute9: Int,
    val reward: Int,
) {
    fun getDiffProperty(): Property {
        return Property(
            attack1 = eventAttribute1,
            attack2 = eventAttribute2,
            attack3 = eventAttribute3,
            defenses = eventAttribute4,
            hp = eventAttribute5,
            fatalRate = eventAttribute6,
            fatalDamage = eventAttribute7,
            dodgeRate = eventAttribute8,
            speed = eventAttribute9
        )
    }
}