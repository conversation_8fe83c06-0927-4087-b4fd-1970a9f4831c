package com.moyu.core.model.damage

import com.moyu.core.AppWrapper
import core.generated.resources.Res
import core.generated.resources.damage1
import core.generated.resources.damage2
import core.generated.resources.damage3
import core.generated.resources.damage4
import core.generated.resources.damage5
import core.generated.resources.defense1
import core.generated.resources.defense_name1
import core.generated.resources.defense_name2
import core.generated.resources.defense_name3
import core.generated.resources.defense_name4
import core.generated.resources.defense_name5

enum class DamageType(val display: String, val defenseDisplay: String, val defenseName: String, val value: Int) {
    DamageType1(AppWrapper.getStringKmp(Res.string.damage1), AppWrapper.getStringKmp(Res.string.defense1),  AppWrapper.getStringKmp(Res.string.defense_name1),1),
    DamageType2(AppWrapper.getStringKmp(Res.string.damage2), AppWrapper.getStringKmp(Res.string.defense1),  AppWrapper.getStringKmp(Res.string.defense_name2), 2),
    DamageType3(AppWrapper.getStringKmp(Res.string.damage3), AppWrapper.getStringKmp(Res.string.defense1),  AppWrapper.getStringKmp(Res.string.defense_name3), 3),
    DamageType4(AppWrapper.getStringKmp(Res.string.damage4), AppWrapper.getStringKmp(Res.string.defense1),  AppWrapper.getStringKmp(Res.string.defense_name4), 4),
    DamageType5(AppWrapper.getStringKmp(Res.string.damage5), AppWrapper.getStringKmp(Res.string.defense1),  AppWrapper.getStringKmp(Res.string.defense_name5), 5),;

    companion object {
        fun fromTypeValue(type: Int): DamageType? {
            return entries.firstOrNull { it.value == type }
        }

        fun fromDisplayValue(display: String): DamageType? {
            return entries.firstOrNull { it.display == display }
        }
    }

    fun getDamageName(): String {
        return when (this) {
            DamageType1 -> DamageType1.display
            DamageType2 -> DamageType2.display
            DamageType3 -> DamageType3.display
            DamageType4 -> DamageType4.display
            DamageType5 -> DamageType5.display
        }
    }
}