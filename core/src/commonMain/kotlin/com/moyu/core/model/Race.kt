package com.moyu.core.model

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.property.Property
import core.generated.resources.Res
import core.generated.resources.group1
import core.generated.resources.group10
import core.generated.resources.group11
import core.generated.resources.group2
import core.generated.resources.group3
import core.generated.resources.group4
import core.generated.resources.group5
import core.generated.resources.group6
import core.generated.resources.group7
import core.generated.resources.group8
import core.generated.resources.group9
import core.generated.resources.race1
import core.generated.resources.race2
import core.generated.resources.race3
import core.generated.resources.race4
import core.generated.resources.race5
import core.generated.resources.skill_tree
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

const val RACE_SIZE = 11

fun Int.getRaceTreeTypeName(): String {
    return "[" + getRaceTypeName() + AppWrapper.getStringKmp(Res.string.skill_tree) + "]"
}

fun Int.getRaceTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.race1)
        2 -> AppWrapper.getStringKmp(Res.string.race2)
        3 -> AppWrapper.getStringKmp(Res.string.race3)
        4 -> AppWrapper.getStringKmp(Res.string.race4)
        else -> AppWrapper.getStringKmp(Res.string.race5)
    }
}

fun Int.getRaceGroupName(): String {
    return when (this) {
        1 -> AppWrapper.getStringKmp(Res.string.group1)
        2 -> AppWrapper.getStringKmp(Res.string.group2)
        3 -> AppWrapper.getStringKmp(Res.string.group3)
        4 -> AppWrapper.getStringKmp(Res.string.group4)
        5 -> AppWrapper.getStringKmp(Res.string.group5)
        6 -> AppWrapper.getStringKmp(Res.string.group6)
        7 -> AppWrapper.getStringKmp(Res.string.group7)
        8 -> AppWrapper.getStringKmp(Res.string.group8)
        9 -> AppWrapper.getStringKmp(Res.string.group9)
        10 -> AppWrapper.getStringKmp(Res.string.group10)
        19 -> "无阵营"
        else -> AppWrapper.getStringKmp(Res.string.group11)
    }
}

fun Int.getRaceType3Name(): String {
    return when (this) {
        1 -> "无甲"
        2 -> "轻甲"
        3 -> "中甲"
        4 -> "重甲"
        5 -> "英雄甲"
        else -> "无甲"
    }
}

@Serializable
data class Race(
    override val id: Int = 1,
    @Transient
    val roleId: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val raceType: Int = 0,
    @Transient
    val raceType2: Int = 0,
    @Transient
    val raceType3: Int = 0, // 所属职业
    @Transient
    val level: Int = 0,
    @Transient
    val star: Int = 0,
    @Transient
    val quality: Int = 0,
    @Transient val attribute1: Int = 0,
    @Transient val attribute2: Int = 0,
    @Transient val attribute3: Int = 0,
    @Transient val attribute4: Int = 0,
    @Transient val attribute5: Int = 0,
    @Transient val attribute6: Double = 0.0,
    @Transient val attribute7: Double = 0.0,
    @Transient val attribute8: Double = 0.0,
    @Transient val attribute9: Int = 0,

    @Transient
    val skillId: List<Int> = emptyList(),
    @Transient
    val randomSkillId: List<Int> = emptyList(),
    @Transient
    val banSkillId: List<Int> = emptyList(),
    @Transient
    val randomSkillNum: List<Int> = emptyList(),
    @Transient
    val pic: String = "",
    @Transient
    val story: String = "",
): ConfigData {
    fun getProperty(): Property {
        return Property(
            attack1 = attribute1,
            attack2 = attribute2,
            attack3 = attribute3,
            defenses = attribute4,
            hp = attribute5,
            fatalRate = attribute6,
            fatalDamage = attribute7,
            dodgeRate = attribute8,
            speed = attribute9
        )
    }

    /**
     * 获取种族的国家索引，返回0-N
     */
    fun getRaceCountryIndex(): Int? {
        return skillId.firstOrNull { it in 4201..4209 }?.let {
            it - 4201
        }
    }

    fun getHeadIcon(): String {
        return pic// + "_headicon"
    }

    fun isMelee(): Boolean {
        return raceType !in listOf(2,4,5)
    }

    fun getAlly() = GameCore.instance.getAllyById(id)
}