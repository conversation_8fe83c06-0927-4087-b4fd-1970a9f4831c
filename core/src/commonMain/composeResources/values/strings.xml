<resources>																			
    <string name="cast_skill">釋放了技能</string>																			
    <string name="gain_permanent_debuff">獲得了永久減益</string>																			
    <string name="gain_permanent_buff">獲得了永久增益</string>																			
    <string name="execute_failed">釋放了斬殺，不過目標免死，斬殺無效</string>																			
    <string name="execute_not_work">釋放了斬殺，但是對方當前血量大於你的最大生命值，斬殺無效</string>																			
    <string name="execute_done">釋放了斬殺，成功擊殺了</string>																			
    <string name="gain_avoid_death">獲得了免死</string>																			
    <string name="avoid_death_failed">獲得了免死,但是對方無視免死,免死無效</string>																			
    <string name="direct_win">被釋放，你直接獲得了戰鬥勝利</string>																			
    <string name="gain_enhancement">獲得了技能強化</string>																			
    <string name="enhancement_skip">但是強化目標技能未學習</string>																			
    <string name="damage_record">傷害記錄: </string>																			
    <string name="attacker_with_comma">進攻方:</string>																			
    <string name="defender_with_comma">防禦方:</string>																			
    <string name="initial_damage_with_comma">初始傷害:</string>																			
    <string name="fatal_with_comma">暴擊:</string>																			
    <string name="fatal_damage_with_comma">暴傷:</string>																			
    <string name="race_damage_with_comma">類型增傷:</string>																			
    <string name="normal_attack_with_comma">普攻增傷:</string>																			
    <string name="skill_damage_with_comma">其他增傷:</string>																			
    <string name="attacker_damage_inc">傷害增減(進攻方):</string>																			
    <string name="attacker_damage_inc_all">所有傷害增減(進攻方):</string>																			
    <string name="pierce_attacker">穿透(進攻方):</string>																			
    <string name="damage_value">傷害值:</string>																			
    <string name="defender_init_defense">防禦方初始防禦</string>																			
    <string name="defender_real_defense">防禦方實際防禦</string>																			
    <string name="defender_reduce_damage">防禦減傷效果(防守方):</string>																			
    <string name="defender_reduce_value">減傷(防禦方):</string>																			
    <string name="defender_race_reduce">防禦方兵種減傷:</string>																			
    <string name="defender_all_reduce">防禦方所有傷害減傷:</string>																			
    <string name="defender_immune">防禦方免疫:</string>																			
    <string name="defender_holy_shield">防禦方聖盾:</string>																			
    <string name="defender_dodge">閃避:</string>																			
    <string name="single_damage">單次傷害上限:</string>																			
    <string name="final_damage">最終傷害:</string>																			
    <string name="single_damage_protect">單次傷害保護:</string>																			
    <string name="yes">是</string>																			
    <string name="no">否</string>																			
    <string name="skill_typ1">物理</string>																			
    <string name="skill_type2">氣系</string>																			
    <string name="skill_type3">火系</string>																			
    <string name="skill_type4">水系</string>																			
    <string name="skill_type5">土系</string>																			
    <string name="ones_turn">的回合:</string>																			
    <string name="over_turn">你沒能在限定回合內擊敗對手，戰鬥失敗</string>																			
    <string name="release">釋放</string>																			
    <string name="gain">獲得</string>																			
    <string name="you_release">你釋放</string>																			
    <string name="let">使</string>																			
    <string name="lost">損失</string>																			
    <string name="control_immune">控制免疫，無法被</string>																			
    <string name="trigger_multi_cast">觸發了士氣高漲</string>																			
    <string name="forbid_heal_tip">受到【驚懼】效果，無法恢復生命。</string>																			
    <string name="frenzy_tips">受到【狂暴】影響，隨機選擇目標。</string>																			
    <string name="disarm_tips">受到【纏繞】效果，無法發動普通攻擊。</string>																			
    <string name="silent_tips">受到【石化】效果，無法釋放主動技能。</string>																			
    <string name="palsy_tips">受到【失明】效果，無法釋放觸發技能。</string>																			
    <string name="ban_skill_tip1">受到【封印</string>																			
    <string name="ban_skill_tip2">】效果，無法釋放該技能。</string>																			
    <string name="heal">恢復</string>																			
    <string name="layer">層</string>																			
    <string name="unstack">不可堆疊</string>																			
    <string name="infinite">無限</string>																			
    <string name="rage">狂暴</string>																			
    <string name="unbreakable">無敵</string>																			
    <string name="prop1">武勇</string>																			
    <string name="prop2">智略</string>																			
    <string name="prop3">統帥</string>																			
    <string name="prop4">內政</string>																			
    <string name="prop5">魅力</string>																			
    <string name="prop6">空一個</string>																			
    <string name="holy_shield">聖盾</string>																			
    <string name="race1">力量型</string>
    <string name="race2">敏捷型</string>
    <string name="race3">智力型</string>
    <string name="race4">施法</string>
    <string name="race5">英雄</string>
    <string name="group1">人族</string>
    <string name="group2">兽人部落</string>
    <string name="group3">暗夜精灵</string>
    <string name="group4">不死族</string>
    <string name="group5">娜迦族</string>
    <string name="group6">巨龙族</string>
    <string name="group7">恶魔族</string>
    <string name="group8">中立</string>
    <string name="group9">元素</string>																			
    <string name="group10">港口</string>																			
    <string name="group11">工廠</string>																			
    <string name="group19">無</string>																			
    <string name="dispelled">驅散了</string>																			
    <string name="cant_dispel">免疫減益驅散，無法驅散</string>																			
    <string name="cant_dispel2">免疫增益驅散，無法驅散</string>																			
    <string name="defeated_the_enemy">你擊敗了敵人</string>																			
    <string name="level">級</string>																			
    <string name="level1">級</string>																			
    <string name="death">死亡</string>																			
    <string name="continuous_damage">持續傷害</string>																			
    <string name="continued_treatment">持續治療</string>																			
    <string name="drink">吸取</string>																			
    <string name="i_achieved_positive_results">我獲得增益</string>																			
    <string name="i_achieved_negative_effects">我獲得減益</string>																			
    <string name="dispel_the_opponent_buff">驅散對方增益</string>																			
    <string name="dispel_your_own_buff">驅散自己增益</string>																			
    <string name="damage_self">傷害自己</string>																			
    <string name="cure_yourself">恢復自己生命</string>																			
    <string name="free_sb_from_death">釋放免死</string>																			
    <string name="release_a_skill">釋放技能</string>																			
    <string name="got_damage">受到傷害</string>																			
    <string name="got_hurt">造成傷害</string>																			
    <string name="successful_block">成功閃避</string>																			
    <string name="start_of_the_round">回合開始</string>																			
    <string name="end_of_the_round">回合結束</string>																			
    <string name="call_upon_a_servant">召喚生物</string>																			
    <string name="no_dispelling_of_target_buffs">無可驅散目標效果</string>																			
    <string name="minion_already_exists">已存在召喚物,無法繼續召喚</string>																			
    <string name="no_position_for_minion">沒有空餘位置</string>																			
    <string name="summoned">召喚了</string>																			
    <string name="designated_skill_strike_rate_increase">暴擊率提升</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">釋放次數提升</string>																			
    <string name="immunity_to_blocking_for_specified_skills">免疫閃避</string>																			
    <string name="designated_skills_ignore_stance">無視護盾</string>																			
    <string name="designated_skills_ignore_invincibility">無視無敵/免死</string>																			
    <string name="designated_skills_ignore_taunts">無視嘲諷</string>																			
    <string name="cannot_be_dispersed">增益/減益無法被驅散</string>																			
    <string name="increased_damage_dealt_by_designated_skills">傷害提高</string>																			
    <string name="designated_skills_deal_less_damage">傷害降低</string>																			
    <string name="designated_skill_cd_increase">冷卻提高</string>																			
    <string name="designated_skill_cd_decrease">冷卻降低</string>																			
    <string name="increased_number_of_rounds">增益/減益持續回合數提高</string>																			
    <string name="decreased_number_of_rounds">增益/減益持續回合數減少</string>																			
    <string name="increased_probability_of_releasing_designated_skills">釋放概率提高</string>																			
    <string name="reduced_probability_of_releasing_designated_skills">釋放概率降低</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">每回合限制釋放次數提高</string>																			
    <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">每回合限制釋放次數降低</string>																			
    <string name="increase_in_the_number_of_restricted_releases">整場戰鬥限制釋放次數提高</string>																			
    <string name="reduced_limit_on_number_of_releases">整場戰鬥限制釋放次數降低</string>																			
    <string name="increased_blood_absorption_rate_of_skills">吸取提高</string>																			
    <string name="designated_skills_ignore_enemies">忽略敵方防禦</string>																			
    <string name="skill_damage_type_changes_to">傷害類型變爲</string>																			
    <string name="designated_skills_vs_race">對類型</string>																			
    <string name="damage_increase">傷害提高</string>																			
    <string name="damage_decrease">傷害降低</string>																			
    <string name="gain_split_effect">獲得【濺射】</string>																			
    <string name="fatal">暴擊</string>																			
    <string name="damage1">普通</string>
    <string name="damage2">穿刺</string>
    <string name="damage3">魔法</string>
    <string name="damage4">混乱</string>
    <string name="damage5">不存在</string>
    <string name="defense1">防禦</string>																			
    <string name="defense2">抗性</string>																			
    <string name="defense_name1">防禦</string>																			
    <string name="defense_name2">氣抗</string>																			
    <string name="defense_name3">火抗</string>																			
    <string name="defense_name4">水抗</string>																			
    <string name="defense_name5">土抗</string>																			
    <string name="attack">攻擊</string>																			
    <string name="attack_tips">決定了各類傷害的強度值</string>																			
    <string name="defense1_tips">受到物理傷害時起到減傷的作用</string>																			
    <string name="hp">生命</string>
    <string name="hp_tips">生命爲零時，英雄或兵種將會陣亡</string>																			
    <string name="fatal_rate">暴擊</string>																			
    <string name="fatal_rate_tips">造成額外大量傷害的概率</string>																			
    <string name="fatal_damage">暴傷</string>																			
    <string name="fatal_damage_tips">觸發暴擊時造成的額外傷害比例</string>																			
    <string name="dodge">閃避</string>																			
    <string name="dodge_tips">成功閃避時，將完全避免本次傷害</string>																			
    <string name="speed">速度</string>																			
    <string name="speed_tips">決定了英雄或兵種行動的優先順序，速度越高越早行動</string>																			
    <string name="skill_tree">系</string>
    <string name="battle_attribute1">力量</string>
    <string name="battle_attribute2">敏捷</string>
    <string name="battle_attribute3">智力</string>
    <string name="battle_attribute4">护甲</string>
    <string name="battle_attribute5">生命</string>
    <string name="battle_attribute6">暴击</string>
    <string name="battle_attribute7">暴伤</string>
    <string name="battle_attribute8">闪避</string>
    <string name="battle_attribute9">速度</string>
    <string name="attack1">力量</string>
    <string name="attack2">敏捷</string>
    <string name="attack3">智力</string>
    <string name="attack1_tips">決定了各類傷害的強度值</string>
    <string name="attack2_tips">決定了各類傷害的強度值</string>
    <string name="attack3_tips">決定了各類傷害的強度值</string>

</resources>