# 主属性功能实现文档

## 概述

本文档描述了新增的主属性功能（枚举10），该功能允许角色拥有一个主属性类型（力量、敏捷或智力），并根据主属性类型来获取或修改对应的属性值。

## 功能特性

### 1. 主属性类型枚举

```kotlin
const val MAIN_ATTRIBUTE_STRENGTH = 1      // 力量
const val MAIN_ATTRIBUTE_AGILITY = 2       // 敏捷  
const val MAIN_ATTRIBUTE_INTELLIGENCE = 3  // 智力
```

### 2. Property类新增字段

在`Property`数据类中新增了`mainAttributeType`字段：

```kotlin
val mainAttributeType: Int = MAIN_ATTRIBUTE_STRENGTH // 主属性类型：1=力量，2=敏捷，3=智力
```

### 3. 核心方法

#### 获取主属性值
```kotlin
fun getMainAttributeValue(): Int
```
根据`mainAttributeType`返回对应的攻击属性值（attack1/attack2/attack3）。

#### 设置主属性值
```kotlin
fun setMainAttributeValue(value: Int): Property
```
根据`mainAttributeType`设置对应的攻击属性值。

#### 修改主属性值
```kotlin
fun changeMainAttributeValue(diff: Int): Property
```
根据`mainAttributeType`修改对应的攻击属性值。

#### 获取主属性类型名称
```kotlin
fun getMainAttributeTypeName(): String
```
返回主属性类型的中文名称（"力量"/"敏捷"/"智力"）。

### 4. 枚举10支持

现在可以通过枚举10来操作主属性：

- `Property.propByIndex(10, diffValue)` - 创建主属性增减的Property，会根据目标的主属性类型来增减对应的属性
- `property.getPropertyByTarget(10)` - 获取主属性值
- `Property.getPropertyByEnum(10, value)` - 不支持，会抛出错误，因为需要知道主属性类型

### 5. 扩展功能

#### 类型判断方法
```kotlin
fun Property.isStrengthType(): Boolean
fun Property.isAgilityType(): Boolean  
fun Property.isIntelligenceType(): Boolean
```

#### 便捷创建方法
```kotlin
fun Property.Companion.createWithMainAttribute(
    mainAttributeType: Int,
    mainAttributeValue: Int = 0,
    otherAttributes: Property = EMPTY_PROPERTY
): Property
```

#### 主属性操作方法
```kotlin
fun Property.addMainAttributeValue(value: Int): Property
fun Property.withMainAttributeValue(value: Int): Property
fun Property.compareMainAttribute(other: Property): Int
fun Property.getMainAttributeRatio(): Double
```

## 使用场景

### 1. 英雄创建
```kotlin
val warriorProperty = Property(
    attack1 = 150,
    attack2 = 80, 
    attack3 = 60,
    mainAttributeType = MAIN_ATTRIBUTE_STRENGTH
)
```

### 2. Buff系统
```kotlin
// 通过枚举10创建主属性增减buff
val mainAttributeBuff = Property.propByIndex(10, 50.0) // 主属性增加50

// 应用buff - 会根据目标的主属性类型自动增减对应的属性
val buffedProperty = originalProperty + mainAttributeBuff
```

### 3. 装备系统
```kotlin
// 根据英雄主属性类型提供对应加成
val enhancedProperty = heroProperty.addMainAttributeValue(equipmentBonus)
```

### 4. 技能系统
```kotlin
// 基于主属性值计算技能伤害
val skillDamage = heroProperty.getMainAttributeValue() * skillMultiplier
```

### 5. UI显示
在`MainPropertyLine`组件中会自动显示主属性信息，包括：
- 对应的属性图标
- 主属性类型名称
- 属性值变化
- 提示信息

## 兼容性

### 向后兼容
- 现有的属性枚举1-9功能保持不变
- 默认主属性类型为力量（MAIN_ATTRIBUTE_STRENGTH）
- 所有现有的Property操作符（+、-、*）都已更新支持主属性类型

### 数据迁移
- 现有的Property实例会自动使用默认的力量主属性类型
- 不需要修改现有的数据结构

## 实现细节

### 1. 属性操作符更新
- `plus`操作符：如果diff中的主属性类型不是默认值，则使用diff的主属性类型
- `minus`操作符：主属性类型保持不变
- `times`操作符：主属性类型保持不变

### 2. 边界检查
- `ensureNotNegative()`方法会确保主属性类型在有效范围内（1-3）

### 3. UI集成
- `MainPropertyLine`组件自动支持主属性显示
- 根据主属性类型显示对应的图标和名称

## 测试

提供了完整的测试示例：
- `PropertyMainAttributeTest.kt` - 基础功能测试
- `MainAttributeUsageExample.kt` - 实际使用场景示例

## 总结

主属性功能为游戏系统提供了更灵活的属性管理方式，允许：
1. 统一的主属性概念，简化属性操作
2. 类型安全的属性访问和修改
3. 与现有系统的无缝集成
4. 丰富的扩展功能和便捷方法

这个实现既满足了新增枚举10的需求，又提供了比简单字段更强大和灵活的解决方案。
