# Uncomment the next line to define a global platform for your project
# platform :ios, '11.0'

target 'iosApp' do
  use_frameworks!
  platform :ios, '14.0'
  
  pod 'shared', :path => '../shared'
  pod 'AFNetworking', '~> 4.0'
  pod 'MBProgressHUD', '~> 1.2.0'
  pod 'SSZipArchive', '~> 2.4.2'
  pod 'CryptoSwift', '~> 1.8.3'
  pod 'Google-Mobile-Ads-SDK'
#  pod 'AppsFlyerFramework'
  # Add the Firebase pod for Google Analytics
  pod 'Firebase/Core'
  pod 'Firebase/Analytics'
  #  pod 'FirebaseCrashlytics'

#  pod 'FBSDKCoreKit'     # 基础模块，必须
  pod 'Adjust'
  
  post_install do |installer|
     # 修改 deployment target
     installer.pods_project.targets.each do |target|
       target.build_configurations.each do |config|
         config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0' # 设置目标版本
       end
     end
   end
end
