//
//  Common.m
//  iosApp
//
//  Created by quding on 2025/1/14.
//  Copyright © 2025 hour. All rights reserved.
//

#import "Common.h"
#import <AudioToolbox/AudioToolbox.h>

@implementation Common


+ (NSString *)transformVersionCode {
    NSMutableString * versionCode = [NSMutableString string];
    // versionCode 1.0.0 转换成 100000
    NSArray *codeArray = [APPVersionCode componentsSeparatedByString:@"."];
    for (NSString *code in codeArray) {
        if (code.length == 1) {
            [versionCode appendFormat:@"0%@",code];
        } else {
            [versionCode appendFormat:@"%@",code];
        }
    }
    return versionCode;
}

@end


@interface KDSoundPlayer ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *soundIDs;

@end

@implementation KDSoundPlayer

+ (instancetype)sharedInstance {
    static KDSoundPlayer *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[KDSoundPlayer alloc] init];
        instance.soundIDs = [NSMutableDictionary dictionary];
    });
    return instance;
}

- (void)playSoundNamed:(NSString *)name {
    NSString *key = [NSString stringWithFormat:@"%@", name];
    NSNumber *soundIDNumber = self.soundIDs[key];
    SystemSoundID soundID;

    if (soundIDNumber) {
        soundID = [soundIDNumber unsignedIntValue];
    } else {
//        NSString *path = [[NSBundle mainBundle] path:name ofType:ext];
        NSString *path = [[NSBundle mainBundle] pathForResource:name ofType:@"" inDirectory:@"compose-resources/composeResources/shared.generated.resources/files"];
        if (!path) {
            NSLog(@"❌ Sound file not found: %@", name);
            return;
        }

        NSURL *url = [NSURL fileURLWithPath:path];
        OSStatus status = AudioServicesCreateSystemSoundID((__bridge CFURLRef)(url), &soundID);
        if (status != kAudioServicesNoError) {
            NSLog(@"❌ Failed to create SystemSoundID for %@: %d", key, (int)status);
            return;
        }
        
        self.soundIDs[key] = @(soundID);
    }

    AudioServicesPlaySystemSound(soundID);
}

- (void)disposeAllSounds {
    for (NSNumber *number in self.soundIDs.allValues) {
        SystemSoundID soundID = [number unsignedIntValue];
        AudioServicesDisposeSystemSoundID(soundID);
    }
    [self.soundIDs removeAllObjects];
}

@end
