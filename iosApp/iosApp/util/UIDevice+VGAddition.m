//
//  UIDevice+VGAddition.m
//  iosApp
//
//  Created by quding on 2025/1/14.
//  Copyright © 2025 hour. All rights reserved.
//

#import "UIDevice+VGAddition.h"

@implementation UIDevice (VGAddition)

/// 顶部安全区高度
+ (CGFloat)vg_safeDistanceTop {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    UIWindow *window = windowScene.windows.firstObject;
    return window.safeAreaInsets.top;
}

/// 底部安全区高度
+ (CGFloat)vg_safeDistanceBottom {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    UIWindow *window = windowScene.windows.firstObject;
    return window.safeAreaInsets.bottom;
}


/// 顶部状态栏高度（包括安全区）
+ (CGFloat)vg_statusBarHeight {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    UIStatusBarManager *statusBarManager = windowScene.statusBarManager;

    return statusBarManager.statusBarFrame.size.height;
}

/// 导航栏高度
+ (CGFloat)vg_navigationBarHeight {
    return 44.0f;
}

/// 状态栏+导航栏的高度
+ (CGFloat)vg_navigationFullHeight {
    return [UIDevice vg_statusBarHeight] + [UIDevice vg_navigationBarHeight];
}

/// 底部导航栏高度
+ (CGFloat)vg_tabBarHeight {
    return 49.0f;
}

/// 底部导航栏高度（包括安全区）
+ (CGFloat)vg_tabBarFullHeight {
    return [UIDevice vg_tabBarHeight] + [UIDevice vg_safeDistanceBottom];
}

@end
