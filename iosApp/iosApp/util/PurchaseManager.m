//
//  PurchaseManager.m
//  iosApp
//
//  Created by quding on 2025/1/6.
//  Copyright © 2025 hour. All rights reserved.
//

#import "PurchaseManager.h"

@interface PurchaseManager ()

@property (nonatomic, assign) IAPPurchaseStatus status;
@property (nonatomic, strong) NSString *receiptData;

@property (nonatomic, strong) NSString *transactionId;
@property (nonatomic, strong) NSString *errMessage;
@property (nonatomic, strong) NSString *productId; // 内购中的产品ID
@property (nonatomic, strong) NSString *payOrderNo; // 业务的订单id

@end

@implementation PurchaseManager

- (void)dealloc {
    [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
}

- (id)init {
    self = [super init];
    if (self) {
        [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
    }
    return self;
}

- (void)removeObserver {
    [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
}

- (void)launchInAppPurchase:(NSString *)productId payOrderNo:(NSString * _Nullable)payOrderNo {
    self.errMessage = nil;
    
    self.productId = productId;
    self.payOrderNo = payOrderNo;
    if ([SKPaymentQueue canMakePayments]) {
        [self requestProductData:productId payOrderNo:payOrderNo];
    } else {
        NSLog(@"不允许程序内付费");
    }
}

- (void)resumeInAppPurchase:(NSString *)productId {
    self.productId = productId;
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
}

- (void)requestProductData:(NSString *)type payOrderNo:(NSString *)payOrderNo {
    NSLog(@"-------------请求对应的产品信息----------------");
    NSArray *product = [[NSArray alloc] initWithObjects:type, nil];
    NSSet *nsset = [NSSet setWithArray:product];
    SKProductsRequest *request = [[SKProductsRequest alloc] initWithProductIdentifiers:nsset];
    request.delegate = self;
    [request start];
}

- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
    NSLog(@"--------------收到产品反馈消息---------------------");
    NSArray *productList = response.products;
    if([productList count] == 0){
        NSLog(@"--------------没有商品------------------");
        if (self.didReceiveProductFailed) self.didReceiveProductFailed();
        return;
    }
    NSLog(@"productID:%@", response.invalidProductIdentifiers);
    SKProduct *tmpProduct = nil;
    for (SKProduct *pro in productList) {
        NSLog(@"%@", [pro description]);
        NSLog(@"%@", [pro localizedTitle]);
        NSLog(@"%@", [pro localizedDescription]);
        NSLog(@"%@", [pro price]);
        NSLog(@"%@", [pro productIdentifier]);
        if([pro.productIdentifier isEqualToString:self.productId]){
            tmpProduct = pro;
        }
    }
    SKMutablePayment *payment = [SKMutablePayment paymentWithProduct:tmpProduct];
    payment.applicationUsername = self.payOrderNo;
    NSLog(@"发送购买请求");
    [[SKPaymentQueue defaultQueue] addPayment:payment];
    // 可以把我们的自己订单和IAP的交易订单绑定,本地存储订单信息
}

- (void)request:(SKRequest *)request didFailWithError:(NSError *)error {
    NSLog(@"------------------错误-----------------:%@", error);
}

- (void)requestDidFinish:(SKRequest *)request {
    NSLog(@"------------反馈信息结束-----------------");
}

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transaction {
    SKPaymentTransaction *tran = [transaction lastObject];
    switch (tran.transactionState) {
        case SKPaymentTransactionStatePurchased:
            NSLog(@"交易完成");
            [self completeTransaction:tran forStatus:IAPPurchaseSucceeded];
            break;
        case SKPaymentTransactionStatePurchasing:
            NSLog(@"商品添加进列表");
            break;
        case SKPaymentTransactionStateDeferred:
            NSLog(@"最终状态未确定");
            [self completeTransaction:tran forStatus:IAPPurchaseFailed];
            break;
        case SKPaymentTransactionStateRestored:
            NSLog(@"已经购买过商品");
            [[SKPaymentQueue defaultQueue] finishTransaction:tran];
            break;
        case SKPaymentTransactionStateFailed:
            NSLog(@"交易失败: %ld", tran.error.code);
            [self completeTransaction:tran forStatus:IAPPurchaseFailed];
            break;
        default:
            break;
    }
}

- (void)completeTransaction:(SKPaymentTransaction *)transaction forStatus:(IAPPurchaseStatus)status {
    self.status = status;
    self.transactionId = @"";
    
    if (status == IAPPurchaseFailed) {
        self.errMessage = [self errorReason:transaction.error];
    } else if (status == IAPPurchaseSucceeded) {
        // 订阅特殊处理
        if (transaction.originalTransaction) {
            // 如果是自动续费的订单,originalTransaction会有内容
            NSLog(@"自动续费的订单,originalTransaction = %@",transaction.originalTransaction);
        } else {
            // 普通购买，以及第一次购买自动订阅
            NSLog(@"普通购买，以及第一次购买自动订阅");
        }
        // 得到该订单的用户Id
//        NSString *orderUserId = [[transaction payment] applicationUsername];
//        // 当前登录人的用户Id
//        NSString *loginUserId = [[LoginUserManager shareInstance].userId decimalDescription];
//        // 当订单的userId和当前userId一致 或者 订单userId为空时才处理票据、执行finish操作
//        if ((orderUserId && orderUserId.length > 0 && [orderUserId isEqual:loginUserId]) ||
//            (!orderUserId || orderUserId.length == 0)) {
//        }
        /// 获取购买凭据
        NSData *receiptData = [NSData dataWithContentsOfURL:[[NSBundle mainBundle] appStoreReceiptURL]];
        self.receiptData = [self encode:receiptData.bytes length:receiptData.length];
        
        /// 订单地址
        self.transactionId = transaction.transactionIdentifier;
        self.payOrderNo = [[transaction payment] applicationUsername];
        NSLog(@"订单ID：%@",self.transactionId);
    }
    
    if (self.paymentTransactionDidFinish) self.paymentTransactionDidFinish();
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
}

- (NSString *)errorReason:(NSError *)error {
    NSString *detail;
    if (error != nil) {
        switch (error.code) {
            case SKErrorUnknown:
                NSLog(@"SKErrorUnknown(未知的错误，您可能正在使用越狱手机)");
                detail = @"未知的错误，您可能正在使用越狱手机";
                break;
            case SKErrorClientInvalid:
                NSLog(@"SKErrorClientInvalid(当前苹果账户无法购买商品(如有疑问，可以询问苹果客服))");
                detail = @"当前苹果账户无法购买商品(如有疑问，可以询问苹果客服)";
                break;
            case SKErrorPaymentCancelled:
                NSLog(@"SKErrorPaymentCancelled(订单已取消)");
                detail = @"订单已取消";
                break;
            case SKErrorPaymentInvalid:
                NSLog(@"SKErrorPaymentInvalid(订单无效(如有疑问，可以询问苹果客服))");
                detail = @"订单无效(如有疑问，可以询问苹果客服)";
                break;
            case SKErrorPaymentNotAllowed:
                NSLog(@"SKErrorPaymentNotAllowed(当前苹果设备无法购买商品(如有疑问，可以询问苹果客服))");
                detail = @"当前苹果设备无法购买商品(如有疑问，可以询问苹果客服)";
                break;
            case SKErrorStoreProductNotAvailable:
                NSLog(@"SKErrorStoreProductNotAvailable(当前商品不可用)");
                detail = @"当前商品不可用";
                break;
            default:
                NSLog(@"No Match Found for error(未知错误)");
                detail = @"未知错误";
                break;
        }
    }
    return detail;
}

- (NSString *)encode:(const uint8_t *)input length:(NSInteger)length {
    static char table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    NSMutableData *data = [NSMutableData dataWithLength:((length + 2) / 3) * 4];
    uint8_t *output = (uint8_t *)data.mutableBytes;
    for (NSInteger i = 0; i < length; i += 3) {
        NSInteger value = 0;
        for (NSInteger j = i; j < (i + 3); j++) {
            value <<= 8;
            if (j < length) {
                value |= (0xFF & input[j]);
            }
        }
        NSInteger index = (i / 3) * 4;
        output[index + 0] =                    table[(value >> 18) & 0x3F];
        output[index + 1] =                    table[(value >> 12) & 0x3F];
        output[index + 2] = (i + 1) < length ? table[(value >> 6)  & 0x3F] : '=';
        output[index + 3] = (i + 2) < length ? table[(value >> 0)  & 0x3F] : '=';
    }
    return [[NSString alloc] initWithData:data encoding:NSASCIIStringEncoding];
}

@end
