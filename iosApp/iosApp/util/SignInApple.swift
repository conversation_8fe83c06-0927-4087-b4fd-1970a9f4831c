//
//  SignInApple.swift
//  iosApp
//
//  Created by quding on 2024/12/30.
//  Copyright © 2024 hour. All rights reserved.
//

import Foundation
import UIKit
import AuthenticationServices
import Security

class SignInApple: NSObject {
    
    @MainActor @objc static let shared = SignInApple()
    
    private override init() {
//        do {
//            let bundleId = Bundle.main.bundleIdentifier!
//            // debug
//            try KeychainItem(service: bundleId, account: "uid").deleteItem()
//        } catch {
//            print("Unable to get userid from keychain.")
//        }
    }
    
    var callback: (()->())?
    
    @objc public
    func handleAuthorizationAppleID(callback:@escaping ()->()) {
        self.callback = callback
        
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        let request = appleIDProvider.createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
}

extension SignInApple: ASAuthorizationControllerDelegate {
    /// - Tag: did_complete_authorization
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        switch authorization.credential {
        case let appleIDCredential as ASAuthorizationAppleIDCredential:
            
            let userIdentifier = appleIDCredential.user
            let fullName = appleIDCredential.fullName?.givenName ?? ""
            let email = appleIDCredential.email ?? ""
            
            
//            print("User ID: \(userIdentifier)")
//            print("email: \(String(describing: email))")
//            print("givenName: \(fullName)")
            
            self.saveUserInKeychain(userIdentifier, fullName, email)
            
            if (self.callback != nil) {
                self.callback?()
            }
        case let passwordCredential as ASPasswordCredential:
            // Sign in using an existing iCloud Keychain credential.
            let username = passwordCredential.user
            let password = passwordCredential.password
        default:
            break
        }
    }
    
    private func saveUserInKeychain(_ uid: String, _ name: String, _ email: String) {
        do {
            let bundleId = Bundle.main.bundleIdentifier!
            
            try KeychainItem(service: bundleId, account: "uid").saveItem(uid)
            
            if !name.isEmpty {
                try KeychainItem(service: bundleId, account: "name").saveItem(name)
            }
            
            if !email.isEmpty {
                try KeychainItem(service: bundleId, account: "email").saveItem(email)
            }
        } catch {
            print("Unable to save userIdentifier to keychain.")
        }
    }
    
    
    @objc func getUserId() -> String? {
        do {
            let bundleId = Bundle.main.bundleIdentifier!

            let uid = try KeychainItem(service: bundleId, account: "uid").readItem()
//            let name = try KeychainItem(service: bundleId, account: "name").readItem()
//            let email = try KeychainItem(service: bundleId, account: "email").readItem()
            
            return uid
        } catch {
            print("Unable to get userid from keychain.")
        }
        
        return nil
    }
    
    
    @objc func getUserName() -> String? {
        do {
            let bundleId = Bundle.main.bundleIdentifier!
            let name = try KeychainItem(service: bundleId, account: "name").readItem()
            
            return name
        } catch {
            print("Unable to get user name from keychain.")
        }
        
        return nil
    }
    
    
    /// - Tag: did_complete_error
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        // Handle error.
    }
}

extension SignInApple: ASAuthorizationControllerPresentationContextProviding {
    /// - Tag: provide_presentation_anchor
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return UIApplication.shared.windows.first!
    }
}

