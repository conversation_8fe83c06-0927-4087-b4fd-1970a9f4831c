//
//  CryptoUtil.swift
//  iosApp
//
//  Created by quding on 2024/12/26.
//  Copyright © 2024 hour. All rights reserved.
//

import Foundation

import CryptoSwift
import Sentry

class CryptoUtil: NSObject {
    
    private static func getPassword(pwd: String) -> [UInt8] {
        var result = ""
        if pwd.count < 32 {
            result = pwd.padding(toLength: 32, withPad: "0", startingAt: 0)
        } else {
            let endIndex = pwd.index(pwd
                .startIndex, offsetBy: 32)
            result = pwd.substring(to: endIndex)
        }
        
        return Array(result.utf8)
    }

    //
    private static func encrypt(content: String, password: String) -> String {
        do {
            /* AES cryptor instance */
            let aes = try AES(key: getPassword(pwd: password), blockMode: ECB(), padding: .pkcs5)

            /* Encrypt Data */
            let inputData = content.data(using: .utf8)!
            let encryptedBytes = try aes.encrypt(inputData.bytes)
            let encryptedData = Data(encryptedBytes)

            /* Decrypt Data */
        //    let decryptedBytes = try aes.decrypt(encryptedData.bytes)
        //    let decryptedData = Data(decryptedBytes)
            
            let result = encryptedData.toHexString().uppercased()
            
            return result
        } catch {
            print("加密失败: \(error)")
            SentrySDK.capture(error: error)
            return ""
        }
    }

    @objc static func p_encrypt(content: String, password: String) -> String {
        return encrypt(content: content, password: password)
    }

    private static func decrypt(content: String, password: String) -> String{
        do {
            /* AES cryptor instance */
            let aes = try AES(key: getPassword(pwd: password), blockMode: ECB(), padding: .pkcs5)

            /* Decrypt Data */
            let decryptedBytes = try aes.decrypt(Data(hex: content).bytes)
            let decryptedData = Data(decryptedBytes)
            
            let result = String(data: decryptedData, encoding: .utf8)!
            
            return result
        } catch {
            print("解密失败: \(error)")
            SentrySDK.capture(error: error)
            return ""
        }
    }

    @objc static func p_decrypt(content: String, password: String) -> String {
        return decrypt(content: content, password: password)
    }
}
