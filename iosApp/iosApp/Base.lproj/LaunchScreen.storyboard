<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23094" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23084"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" image="launcher_bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="lYf-UL-59n">
                                <rect key="frame" x="76" y="362" width="240" height="128"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="120" id="RVI-b5-ZkZ"/>
                                    <constraint firstAttribute="width" constant="120" id="aNP-4z-0vN"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" image="ic_launcher.png" translatesAutoresizingMaskIntoConstraints="NO" id="XqC-Xj-HMb">
                                <rect key="frame" x="166.66666666666666" y="396.66666666666674" width="60" height="60"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="60.000000000015007" id="Fkl-c5-Jkm"/>
                                    <constraint firstAttribute="width" constant="60" id="hvW-e2-LnF"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="lYf-UL-59n" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="Rjm-dP-goG"/>
                            <constraint firstItem="XqC-Xj-HMb" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="Sti-Zs-ofT"/>
                            <constraint firstItem="lYf-UL-59n" firstAttribute="centerY" secondItem="6Tk-OE-BBY" secondAttribute="centerY" id="W62-Fk-fWd"/>
                            <constraint firstItem="XqC-Xj-HMb" firstAttribute="centerY" secondItem="6Tk-OE-BBY" secondAttribute="centerY" id="sHR-mX-4vT"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.671755725190835" y="374.64788732394368"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_launcher.png" width="511" height="511"/>
        <image name="launcher_bg.png" width="240" height="240"/>
    </resources>
</document>
