// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1E1F160F21AF8EF700A8CDF1 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E1F160E21AF8EF700A8CDF1 /* AppDelegate.m */; };
		1E1F161221AF8EF700A8CDF1 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E1F161121AF8EF700A8CDF1 /* ViewController.m */; };
		1E1F161521AF8EF700A8CDF1 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1E1F161321AF8EF700A8CDF1 /* Main.storyboard */; };
		1E1F161721AF8EF800A8CDF1 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1E1F161621AF8EF800A8CDF1 /* Assets.xcassets */; };
		1E1F161A21AF8EF800A8CDF1 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1E1F161821AF8EF800A8CDF1 /* LaunchScreen.storyboard */; };
		1E1F161D21AF8EF800A8CDF1 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E1F161C21AF8EF800A8CDF1 /* main.m */; };
		463B61272D36A70C00E62C93 /* UIDevice+VGAddition.m in Sources */ = {isa = PBXBuildFile; fileRef = 463B61262D36A70C00E62C93 /* UIDevice+VGAddition.m */; };
		463B612A2D36B09C00E62C93 /* Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 463B61292D36B09C00E62C93 /* Common.m */; };
		465FDD672D22741200473CA6 /* SignInApple.swift in Sources */ = {isa = PBXBuildFile; fileRef = 465FDD662D22741200473CA6 /* SignInApple.swift */; };
		465FDD692D2275EB00473CA6 /* KeychainItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 465FDD682D2275EB00473CA6 /* KeychainItem.swift */; };
		466976812D3C069200B5C501 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4669767F2D3C069200B5C501 /* Localizable.strings */; };
		466976842D3C071C00B5C501 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 466976822D3C071C00B5C501 /* InfoPlist.strings */; };
		467B51922D1C1067007090D1 /* TimeUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 467B51912D1C1067007090D1 /* TimeUtil.m */; };
		46884A172D389C3800653234 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 46884A162D389C3800653234 /* WebKit.framework */; };
		469C6D6E2D1FD8FF00BDE877 /* ic_launcher.png in Resources */ = {isa = PBXBuildFile; fileRef = 469C6D6D2D1FD8FF00BDE877 /* ic_launcher.png */; };
		46B689892D3DDA4E00EFF1A3 /* launcher_bg.png in Resources */ = {isa = PBXBuildFile; fileRef = 46B689882D3DDA4E00EFF1A3 /* launcher_bg.png */; };
		46C584EF2DBB649D00292628 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 46C584EE2DBB649D00292628 /* AdSupport.framework */; };
		46D0B6AE2D1D25F500E69630 /* CryptoUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 46D0B6AD2D1D25F500E69630 /* CryptoUtil.swift */; };
		46E1916B2D2C02C200013942 /* PurchaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 46E1916A2D2C02C200013942 /* PurchaseManager.m */; };
		46FFFCD22D265D030038A802 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 46FFFCD12D265D030038A802 /* GoogleService-Info.plist */; };
		E90E43699A241AA4F331D770 /* Pods_iosApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D41DDC44C6BA80D06666B85 /* Pods_iosApp.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		154F743008B618D95F09E8D6 /* Pods-iosApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iosApp.debug.xcconfig"; path = "Target Support Files/Pods-iosApp/Pods-iosApp.debug.xcconfig"; sourceTree = "<group>"; };
		1E1F160A21AF8EF700A8CDF1 /* iosApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iosApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1E1F160D21AF8EF700A8CDF1 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		1E1F160E21AF8EF700A8CDF1 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		1E1F161021AF8EF700A8CDF1 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		1E1F161121AF8EF700A8CDF1 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		1E1F161421AF8EF700A8CDF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		1E1F161621AF8EF800A8CDF1 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1E1F161921AF8EF800A8CDF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		1E1F161B21AF8EF800A8CDF1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1E1F161C21AF8EF800A8CDF1 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		463B61252D36A70C00E62C93 /* UIDevice+VGAddition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIDevice+VGAddition.h"; sourceTree = "<group>"; };
		463B61262D36A70C00E62C93 /* UIDevice+VGAddition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+VGAddition.m"; sourceTree = "<group>"; };
		463B61282D36B09C00E62C93 /* Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Common.h; sourceTree = "<group>"; };
		463B61292D36B09C00E62C93 /* Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Common.m; sourceTree = "<group>"; };
		465FDD602D224FA300473CA6 /* iosApp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = iosApp.entitlements; sourceTree = "<group>"; };
		465FDD662D22741200473CA6 /* SignInApple.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignInApple.swift; sourceTree = "<group>"; };
		465FDD682D2275EB00473CA6 /* KeychainItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainItem.swift; sourceTree = "<group>"; };
		466976802D3C069200B5C501 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		466976832D3C071C00B5C501 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		466976852D3C07F900B5C501 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		4669768B2D3C089700B5C501 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		4669768C2D3C089700B5C501 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		4669768D2D3C089700B5C501 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		467B51902D1C1067007090D1 /* TimeUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TimeUtil.h; sourceTree = "<group>"; };
		467B51912D1C1067007090D1 /* TimeUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TimeUtil.m; sourceTree = "<group>"; };
		46884A162D389C3800653234 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		469C6D6D2D1FD8FF00BDE877 /* ic_launcher.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = ic_launcher.png; sourceTree = "<group>"; };
		46B689882D3DDA4E00EFF1A3 /* launcher_bg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = launcher_bg.png; sourceTree = "<group>"; };
		46C584EE2DBB649D00292628 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		46D0B6AC2D1D25F500E69630 /* iosApp-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "iosApp-Bridging-Header.h"; sourceTree = "<group>"; };
		46D0B6AD2D1D25F500E69630 /* CryptoUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoUtil.swift; sourceTree = "<group>"; };
		46E191692D2C02C200013942 /* PurchaseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PurchaseManager.h; sourceTree = "<group>"; };
		46E1916A2D2C02C200013942 /* PurchaseManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PurchaseManager.m; sourceTree = "<group>"; };
		46FABCAD2D66D4BC00F2FFAF /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		46FABCAE2D66D4BC00F2FFAF /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		46FFFCD12D265D030038A802 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		7154C7A6507C3E91E5532042 /* Pods-TestObjC.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TestObjC.debug.xcconfig"; path = "Target Support Files/Pods-TestObjC/Pods-TestObjC.debug.xcconfig"; sourceTree = "<group>"; };
		84C1F3B52E225C9C00718764 /* id */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = id; path = id.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		84CE70E62E26532A0049E1D0 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/Localizable.strings"; sourceTree = "<group>"; };
		84CE70E72E26532A0049E1D0 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		8D41DDC44C6BA80D06666B85 /* Pods_iosApp.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_iosApp.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BB1A269C75CEA344BEF74EBF /* Pods-iosApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iosApp.release.xcconfig"; path = "Target Support Files/Pods-iosApp/Pods-iosApp.release.xcconfig"; sourceTree = "<group>"; };
		DAA62C2B44029567AD052A1A /* Pods-TestObjC.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TestObjC.release.xcconfig"; path = "Target Support Files/Pods-TestObjC/Pods-TestObjC.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1E1F160721AF8EF700A8CDF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46884A172D389C3800653234 /* WebKit.framework in Frameworks */,
				E90E43699A241AA4F331D770 /* Pods_iosApp.framework in Frameworks */,
				46C584EF2DBB649D00292628 /* AdSupport.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1E1F160121AF8EF700A8CDF1 = {
			isa = PBXGroup;
			children = (
				1E1F160C21AF8EF700A8CDF1 /* iosApp */,
				1E1F160B21AF8EF700A8CDF1 /* Products */,
				C40FEF4C463438AD2751E5E8 /* Pods */,
				EB41F41EED06298EC91E6F9E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1E1F160B21AF8EF700A8CDF1 /* Products */ = {
			isa = PBXGroup;
			children = (
				1E1F160A21AF8EF700A8CDF1 /* iosApp.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1E1F160C21AF8EF700A8CDF1 /* iosApp */ = {
			isa = PBXGroup;
			children = (
				46FFFCD12D265D030038A802 /* GoogleService-Info.plist */,
				465FDD602D224FA300473CA6 /* iosApp.entitlements */,
				469C6D6B2D1FD5FE00BDE877 /* Res */,
				467B518F2D1C1042007090D1 /* util */,
				1E1F160D21AF8EF700A8CDF1 /* AppDelegate.h */,
				1E1F160E21AF8EF700A8CDF1 /* AppDelegate.m */,
				1E1F161021AF8EF700A8CDF1 /* ViewController.h */,
				1E1F161121AF8EF700A8CDF1 /* ViewController.m */,
				1E1F161321AF8EF700A8CDF1 /* Main.storyboard */,
				1E1F161621AF8EF800A8CDF1 /* Assets.xcassets */,
				1E1F161821AF8EF800A8CDF1 /* LaunchScreen.storyboard */,
				1E1F161B21AF8EF800A8CDF1 /* Info.plist */,
				1E1F161C21AF8EF800A8CDF1 /* main.m */,
				4669767F2D3C069200B5C501 /* Localizable.strings */,
				466976822D3C071C00B5C501 /* InfoPlist.strings */,
			);
			path = iosApp;
			sourceTree = "<group>";
		};
		467B518F2D1C1042007090D1 /* util */ = {
			isa = PBXGroup;
			children = (
				465FDD682D2275EB00473CA6 /* KeychainItem.swift */,
				467B51902D1C1067007090D1 /* TimeUtil.h */,
				467B51912D1C1067007090D1 /* TimeUtil.m */,
				46D0B6AD2D1D25F500E69630 /* CryptoUtil.swift */,
				46D0B6AC2D1D25F500E69630 /* iosApp-Bridging-Header.h */,
				465FDD662D22741200473CA6 /* SignInApple.swift */,
				46E191692D2C02C200013942 /* PurchaseManager.h */,
				46E1916A2D2C02C200013942 /* PurchaseManager.m */,
				463B61252D36A70C00E62C93 /* UIDevice+VGAddition.h */,
				463B61262D36A70C00E62C93 /* UIDevice+VGAddition.m */,
				463B61282D36B09C00E62C93 /* Common.h */,
				463B61292D36B09C00E62C93 /* Common.m */,
			);
			path = util;
			sourceTree = "<group>";
		};
		469C6D6B2D1FD5FE00BDE877 /* Res */ = {
			isa = PBXGroup;
			children = (
				46B689882D3DDA4E00EFF1A3 /* launcher_bg.png */,
				469C6D6D2D1FD8FF00BDE877 /* ic_launcher.png */,
			);
			path = Res;
			sourceTree = "<group>";
		};
		C40FEF4C463438AD2751E5E8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				7154C7A6507C3E91E5532042 /* Pods-TestObjC.debug.xcconfig */,
				DAA62C2B44029567AD052A1A /* Pods-TestObjC.release.xcconfig */,
				154F743008B618D95F09E8D6 /* Pods-iosApp.debug.xcconfig */,
				BB1A269C75CEA344BEF74EBF /* Pods-iosApp.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		EB41F41EED06298EC91E6F9E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				46C584EE2DBB649D00292628 /* AdSupport.framework */,
				46884A162D389C3800653234 /* WebKit.framework */,
				8D41DDC44C6BA80D06666B85 /* Pods_iosApp.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1E1F160921AF8EF700A8CDF1 /* iosApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1E1F162021AF8EF800A8CDF1 /* Build configuration list for PBXNativeTarget "iosApp" */;
			buildPhases = (
				6CF2E09B5E077E1F65F6F31A /* [CP] Check Pods Manifest.lock */,
				1E1F160621AF8EF700A8CDF1 /* Sources */,
				1E1F160721AF8EF700A8CDF1 /* Frameworks */,
				1E1F160821AF8EF700A8CDF1 /* Resources */,
				1170203A2B71F1B8D6D9A6E1 /* [CP] Copy Pods Resources */,
				B1176D4F79039BA04E5D5CCA /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = iosApp;
			productName = TestObjC;
			productReference = 1E1F160A21AF8EF700A8CDF1 /* iosApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1E1F160221AF8EF700A8CDF1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1600;
				ORGANIZATIONNAME = hour;
				TargetAttributes = {
					1E1F160921AF8EF700A8CDF1 = {
						CreatedOnToolsVersion = 10.1;
						LastSwiftMigration = 1600;
					};
				};
			};
			buildConfigurationList = 1E1F160521AF8EF700A8CDF1 /* Build configuration list for PBXProject "iosApp" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				de,
				ja,
				ko,
				es,
				id,
				"pt-PT",
			);
			mainGroup = 1E1F160121AF8EF700A8CDF1;
			productRefGroup = 1E1F160B21AF8EF700A8CDF1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1E1F160921AF8EF700A8CDF1 /* iosApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1E1F160821AF8EF700A8CDF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1E1F161A21AF8EF800A8CDF1 /* LaunchScreen.storyboard in Resources */,
				466976842D3C071C00B5C501 /* InfoPlist.strings in Resources */,
				466976812D3C069200B5C501 /* Localizable.strings in Resources */,
				1E1F161721AF8EF800A8CDF1 /* Assets.xcassets in Resources */,
				46B689892D3DDA4E00EFF1A3 /* launcher_bg.png in Resources */,
				46FFFCD22D265D030038A802 /* GoogleService-Info.plist in Resources */,
				1E1F161521AF8EF700A8CDF1 /* Main.storyboard in Resources */,
				469C6D6E2D1FD8FF00BDE877 /* ic_launcher.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1170203A2B71F1B8D6D9A6E1 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-iosApp/Pods-iosApp-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-iosApp/Pods-iosApp-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-iosApp/Pods-iosApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6CF2E09B5E077E1F65F6F31A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-iosApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B1176D4F79039BA04E5D5CCA /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-iosApp/Pods-iosApp-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-iosApp/Pods-iosApp-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-iosApp/Pods-iosApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1E1F160621AF8EF700A8CDF1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				465FDD692D2275EB00473CA6 /* KeychainItem.swift in Sources */,
				467B51922D1C1067007090D1 /* TimeUtil.m in Sources */,
				463B612A2D36B09C00E62C93 /* Common.m in Sources */,
				46E1916B2D2C02C200013942 /* PurchaseManager.m in Sources */,
				465FDD672D22741200473CA6 /* SignInApple.swift in Sources */,
				1E1F161221AF8EF700A8CDF1 /* ViewController.m in Sources */,
				46D0B6AE2D1D25F500E69630 /* CryptoUtil.swift in Sources */,
				1E1F161D21AF8EF800A8CDF1 /* main.m in Sources */,
				463B61272D36A70C00E62C93 /* UIDevice+VGAddition.m in Sources */,
				1E1F160F21AF8EF700A8CDF1 /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		1E1F161321AF8EF700A8CDF1 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1E1F161421AF8EF700A8CDF1 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		1E1F161821AF8EF800A8CDF1 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1E1F161921AF8EF800A8CDF1 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		4669767F2D3C069200B5C501 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				466976802D3C069200B5C501 /* en */,
				46FABCAD2D66D4BC00F2FFAF /* es */,
				84CE70E62E26532A0049E1D0 /* pt-PT */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		466976822D3C071C00B5C501 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				466976832D3C071C00B5C501 /* en */,
				466976852D3C07F900B5C501 /* zh-Hans */,
				4669768B2D3C089700B5C501 /* de */,
				4669768C2D3C089700B5C501 /* ja */,
				4669768D2D3C089700B5C501 /* ko */,
				46FABCAE2D66D4BC00F2FFAF /* es */,
				84C1F3B52E225C9C00718764 /* id */,
				84CE70E72E26532A0049E1D0 /* pt-PT */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1E1F161E21AF8EF800A8CDF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		1E1F161F21AF8EF800A8CDF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1E1F162121AF8EF800A8CDF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 154F743008B618D95F09E8D6 /* Pods-iosApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = iosApp/iosApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = LJW5DW2J8P;
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Dragon's Treasure";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.games";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1.22;
				PRODUCT_BUNDLE_IDENTIFIER = com.moyu.chuanqirensheng;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "iosApp/util/iosApp-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		1E1F162221AF8EF800A8CDF1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BB1A269C75CEA344BEF74EBF /* Pods-iosApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = iosApp/iosApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = LJW5DW2J8P;
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Dragon's Treasure";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.games";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1.22;
				PRODUCT_BUNDLE_IDENTIFIER = com.moyu.chuanqirensheng;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = adhoc_yingxiongwudi;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "iosApp/util/iosApp-Bridging-Header.h";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1E1F160521AF8EF700A8CDF1 /* Build configuration list for PBXProject "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1E1F161E21AF8EF800A8CDF1 /* Debug */,
				1E1F161F21AF8EF800A8CDF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1E1F162021AF8EF800A8CDF1 /* Build configuration list for PBXNativeTarget "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1E1F162121AF8EF800A8CDF1 /* Debug */,
				1E1F162221AF8EF800A8CDF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1E1F160221AF8EF700A8CDF1 /* Project object */;
}
