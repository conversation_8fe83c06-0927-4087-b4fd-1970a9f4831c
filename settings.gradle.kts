enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    repositories {
        google()
        gradlePluginPortal()
        mavenCentral()
    }
}

plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.9.0"
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs("src/main/libs")
        }
        maven("https://repo1.maven.org/maven2/")
        maven("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        maven("https://artifact.bytedance.com/repository/pangle")
        maven("https://jogamp.org/deployment/maven")
        maven {
            url = uri("https://repos.xiaomi.com/maven")
            credentials {
                username = "mi-gamesdk"
                password = "AKCp8mYeLuhuaGj6bK1XK7t2w4CsPuGwg6GpQdZ9cat7K59y5sD7Tx3dHjJcFrBGj3TQ4vi7g"
            }
        }
    }
}

rootProject.name = "Composed"
include(":core")
include(":shared")
include(":desktopApp")